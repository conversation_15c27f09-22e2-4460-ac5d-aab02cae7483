<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

defineOptions({
  name: 'DisplayRegionsModal',
});

const regionNameOptions = ref([]);

const [Form, formApi] = useVbenForm({
  schema: [
    {
      labelWidth: 80,
      component: 'Select',
      componentProps: {
        class: 'w-full',
        placeholder: '暂无区服',
        options: regionNameOptions,
        mode: 'multiple',
        disabled: true,
      },
      fieldName: 'region_names',
      label: '区服名称',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: () => {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const { options, regions, regionGroupCode } = modalApi.getData();
      formApi.setValues({ region_names: regions });
      regionNameOptions.value = options;
      modalApi.setState({ title: `展示分组${regionGroupCode}区服` });
    }
  },
  title: '展示分组区服',
});
</script>
<template>
  <Modal>
    <Form class="mt-4" />
  </Modal>
</template>
