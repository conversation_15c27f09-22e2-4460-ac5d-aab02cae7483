<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { updateRegionGroupsApi } from '#/api/region_group/region_group';

defineOptions({
  name: 'EditGroupModal',
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
        disabled: true,
      },
      fieldName: 'code',
      label: '分组代号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'name',
      label: '分组名称',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'description',
      label: '描述',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const { row } = modalApi.getData();
      formApi.setValues(row);
    }
  },
  title: '编辑分组表单',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  updateRegionGroupsApi(values.code, {
    name: values.name,
    description: values.description,
  }).then(() => {
    modalApi.close();
    message.success({
      content: `编辑分组${values.code}成功`,
      duration: 2,
    });
    const { callback } = modalApi.getData();
    callback();
  });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
