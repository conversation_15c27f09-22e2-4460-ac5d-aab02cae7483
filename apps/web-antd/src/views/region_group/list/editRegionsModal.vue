<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  addRegionGroupsApi,
  removeRegionGroupsApi,
} from '#/api/region_group/region_group';

defineOptions({
  name: 'EditRegionsModal',
});

const editMode = ref('add');
const regionNameOptions = ref([]);

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      labelWidth: 80,
      component: 'Select',
      componentProps: {
        class: 'w-full',
        placeholder: '请选择',
        options: regionNameOptions,
        mode: 'multiple',
      },
      fieldName: 'region_names',
      label: '区服名称',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const { mode, options, regionGroupCode } = modalApi.getData();
      editMode.value = mode;
      regionNameOptions.value = options;
      const title =
        mode === 'add'
          ? `新增分组${regionGroupCode}区服`
          : `删除分组${regionGroupCode}区服`;
      modalApi.setState({ title });
    }
  },
  title: '修改分组区服',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  const { callback, regionGroupCode } = modalApi.getData();
  if (editMode.value === 'add') {
    addRegionGroupsApi(regionGroupCode, {
      region_names: values.region_names,
    }).then(() => {
      modalApi.close();
      message.success({
        content: `新增分组${regionGroupCode}区服成功`,
        duration: 2,
      });
      callback();
    });
  } else if (editMode.value === 'remove') {
    removeRegionGroupsApi(regionGroupCode, {
      region_names: values.region_names,
    }).then(() => {
      modalApi.close();
      message.success({
        content: `移除分组${regionGroupCode}区服成功`,
        duration: 2,
      });
      callback();
    });
  }
}
</script>
<template>
  <Modal> <Form class="mt-4" /> </Modal>
</template>
