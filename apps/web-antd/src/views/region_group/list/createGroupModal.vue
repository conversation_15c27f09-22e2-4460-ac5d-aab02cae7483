<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createRegionGroupsApi } from '#/api/region_group/region_group';

defineOptions({
  name: 'CreateGroupModal',
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'code',
      label: '分组代号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'name',
      label: '分组名称',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'description',
      label: '描述',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  title: '创建分组表单',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  createRegionGroupsApi({
    name: values.name,
    code: values.code,
    description: values.description,
  })
    .then(() => {
      modalApi.close();
      message.success({
        content: `创建分组${values.name}成功`,
        duration: 2,
      });
      const { callback } = modalApi.getData();
      callback();
    })
    .catch(() => {
      modalApi.unlock();
    });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
