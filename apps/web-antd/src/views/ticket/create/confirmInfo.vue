<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Card, Descriptions, DescriptionsItem } from 'ant-design-vue';

import { getPipelineTemplateApi } from '#/api/pipeline_template/template';
import { PipelineExecDetail } from '#/components/pipelineExecDetail';

const props = defineProps({
  basicInfo: {
    type: Object,
    required: true,
    default: () => ({
      name: '',
      description: '',
      template: '',
    }),
  },
  pipelineData: {
    type: Object,
    required: true,
    default: () => ({
      stages: [],
    }),
  },
});

const templateName = ref<string>('');

// 根据表单类型获取步骤名称
const getTemplateName = async (code: string) => {
  if (!code) {
    return;
  }
  await getPipelineTemplateApi(code).then((data) => {
    templateName.value = data.name;
  });
};

onMounted(async () => {
  await getTemplateName(props.basicInfo.template);
});
</script>

<template>
  <div>
    <Card title="工单基本信息" class="mb-4">
      <Descriptions bordered :column="2">
        <DescriptionsItem label="工单名称">
          {{ props.basicInfo.name }}
        </DescriptionsItem>
        <DescriptionsItem label="工单模板">
          {{ templateName }}
        </DescriptionsItem>
        <DescriptionsItem label="工单描述" :span="2">
          {{ props.basicInfo.description }}
        </DescriptionsItem>
      </Descriptions>
    </Card>
    <Card title="工单流程预览">
      <PipelineExecDetail :pipeline-data="props.pipelineData" />
    </Card>
  </div>
</template>
<style scoped>
/* 覆盖流水线配置组件中虚线的位置 */
:deep .scroll-container::before {
  top: 103px;
}
</style>
