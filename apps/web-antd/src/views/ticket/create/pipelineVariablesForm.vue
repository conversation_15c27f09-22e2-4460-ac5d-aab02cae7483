<script lang="ts" setup>
import type { PipelineTemplateDetail } from '#/api/pipeline_template/type';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { <PERSON><PERSON>, Collapse, CollapsePanel } from 'ant-design-vue';

import { getPipelineTemplateApi } from '#/api/pipeline_template/template';
import { DynamicForm } from '#/components/dynamicForm';
import { GenerateDateStr } from '#/utils/date';

const props = defineProps<{
  template: string;
}>();

const activeKey = ref<string[]>([]);

const formRefs = ref<InstanceType<typeof DynamicForm>[]>([]);

const forms = ref<any[]>([]);

const templateData = ref<PipelineTemplateDetail>();
const templateDataLoading = ref<boolean>(true);

const getPipelineTemplateDetail = async () => {
  templateDataLoading.value = true;
  await getPipelineTemplateApi(props.template).then((data) => {
    templateDataLoading.value = false;
    templateData.value = data;
  });
};

// Add a new form
const addForm = () => {
  forms.value.push({});
  activeKey.value.push(String(forms.value.length));
};

// Remove a form by id
const removeForm = (id: string) => {
  const index = Number.parseInt(id) - 1;
  forms.value.splice(index, 1);
  activeKey.value = activeKey.value.filter((key) => key !== String(index + 1));
};

const setItemRef = (el: any, index: number) => {
  if (index >= formRefs.value.length && el) {
    formRefs.value.push(el);
  } else if (el === null) {
    formRefs.value.splice(index, 1);
  }
};

const schemaJSON = computed(() => {
  return templateData.value?.form;
});

onMounted(async () => {
  // 默认展开全部
  activeKey.value = forms.value.map((_, index) => String(index + 1));
  if (props.template) {
    await getPipelineTemplateDetail();
  }
});

defineExpose({
  values: async (prefix: string) => {
    return await Promise.all(
      formRefs.value?.map(async (form) => {
        const region = await form.region();
        const values = await form.values();
        return {
          name: `${prefix}${region}-${templateData.value?.name}-${GenerateDateStr(
            new Date(),
          )}`,
          variables: values,
        };
      }),
    );
  },
  validate: async () => {
    return await Promise.all(
      formRefs.value?.map((form) => {
        return form.validate();
      }),
    );
  },
});

watch(
  () => props.template,
  async () => {
    if (!props.template) {
      return;
    }
    await getPipelineTemplateDetail();
    forms.value = [];
    activeKey.value = [];
    nextTick(() => {
      addForm();
    });
  },
);
</script>

<template>
  <Collapse v-model:active-key="activeKey" v-spinning="templateDataLoading">
    <CollapsePanel
      v-for="(_, index) in forms"
      :key="index + 1"
      :header="`【${templateData?.name}】表单${index + 1}`"
    >
      <DynamicForm :schema="schemaJSON" :ref="(el) => setItemRef(el, index)" />
      <template #extra>
        <DeleteOutlined
          @click="
            (e: MouseEvent) => {
              e.stopPropagation();
              removeForm(String(index + 1));
            }
          "
        />
      </template>
    </CollapsePanel>
  </Collapse>
  <Button type="dashed" @click="addForm()" class="w-full">
    <PlusOutlined /> 添加表单
  </Button>
</template>
