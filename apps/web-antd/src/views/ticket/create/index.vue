<script setup lang="ts">
import type { BasicForm } from './type';

import { reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { But<PERSON>, Card, message, Steps } from 'ant-design-vue';

import { getPipelineTemplateApi } from '#/api/pipeline_template/template';
import { createTicketApi } from '#/api/ticket/ticket';

import BasicInfoForm from './basicInfoForm.vue';
import ConfirmInfo from './confirmInfo.vue';
import PipelineVariablesForm from './pipelineVariablesForm.vue';

const router = useRouter();
const current = ref<number>(0);
const basicForm = reactive<BasicForm>({
  name: '',
  description: '',
  template: '',
});

const next = async () => {
  if (current.value === 0) {
    const res = await basicInfoForm.value?.validate();
    if (!res?.valid) {
      return;
    }
    const values = await basicInfoForm.value?.values();
    if (values) {
      basicForm.name = values.name;
      basicForm.description = values.description;
      basicForm.template = values.template;
    }
  }
  if (current.value === 1) {
    const res = await variableForm.value?.validate();
    if (!res?.every((item) => item.valid)) {
      return;
    }
  }
  current.value++;
};
const prev = () => {
  current.value--;
};
const steps = [
  {
    title: '填写工单基础信息',
  },
  {
    title: '填写流程参数',
  },
  {
    title: '确认信息',
  },
];
const items = steps.map((item) => ({ key: item.title, title: item.title }));
const pipelineTemplateId = ref<number>();
const basicInfoForm = ref<InstanceType<typeof BasicInfoForm>>();
const variableForm = ref<InstanceType<typeof PipelineVariablesForm>>();
const pipelineConfig = ref({
  stages: [],
});

watch(current, async (val) => {
  if (val === 2) {
    await getPipelineConfig();
  }
});

const getPipelineConfig = async () => {
  if (!basicForm.template) {
    return {
      stages: [],
    };
  }
  await getPipelineTemplateApi(basicForm.template).then((data) => {
    pipelineConfig.value = JSON.parse(data.flow);
    pipelineTemplateId.value = data.id;
  });
};

const getPipelinesData = async () => {
  const name = basicForm.name;
  const prefix = `【工单${name}】`;
  return await variableForm.value?.values(prefix);
};

const submit = async () => {
  const pipelines = await getPipelinesData();
  const submitData = {
    stages: pipelineConfig.value.stages,
    name: basicForm.name,
    description: basicForm.description,
    pipeline_template_id: pipelineTemplateId.value,
    pipelines,
  };
  createTicketApi(submitData).then((data) => {
    message.success('创建工单成功');
    router.push({ name: 'tickets_detail', params: { id: data.id } });
  });
};
</script>
<template>
  <Page auto-content-height title="创建工单" description="根据流程模板创建工单">
    <Card style="height: calc(100% - 50px)" :body-style="{ height: '100%' }">
      <Steps :current="current" :items="items" />
      <div v-show="current === 0" class="mt-12">
        <BasicInfoForm ref="basicInfoForm" />
      </div>
      <div
        v-show="current === 1"
        class="mt-12"
        style="height: calc(100% - 100px); overflow-y: auto"
      >
        <PipelineVariablesForm
          ref="variableForm"
          :template="basicForm.template"
        />
      </div>
      <div v-if="current === 2" class="mt-12">
        <ConfirmInfo :basic-info="basicForm" :pipeline-data="pipelineConfig" />
      </div>
    </Card>
    <div class="mt-4 flex justify-end">
      <Button v-if="current > 0" @click="prev" class="mr-4">上一步</Button>
      <Button
        v-if="current < steps.length - 1"
        type="primary"
        @click="next"
        class="mr"
      >
        下一步
      </Button>
      <Button
        v-if="current === steps.length - 1"
        @click="submit"
        type="primary"
      >
        创建任务
      </Button>
    </div>
  </Page>
</template>
