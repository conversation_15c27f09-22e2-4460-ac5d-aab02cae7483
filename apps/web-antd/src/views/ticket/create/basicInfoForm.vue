<script lang="ts" setup>
import type { BasicForm } from './type';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '#/adapter/form';
import { queryPipelineTemplateApi } from '#/api/pipeline_template/template';

const templateOptions = ref([]);

const getPipelineTemplateOptions = async () => {
  templateOptions.value = await queryPipelineTemplateApi('', '', 1, 200).then(
    (data) => {
      return data.list.map((item: any) => ({
        label: item.name,
        value: item.code,
      }));
    },
  );
};

const schema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '工单名称',
    rules: 'required',
  },
  {
    component: 'Textarea',
    fieldName: 'description',
    label: '工单描述',
    componentProps: {
      rows: 4,
    },
  },
  {
    component: 'Select',
    fieldName: 'template',
    label: '工单模板',
    rules: 'required',
    componentProps: {
      showSearch: true,
      options: templateOptions,
    },
  },
];

const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema,
  showDefaultActions: false,
});

defineExpose({
  values: async () => {
    return (await formApi.getValues()) as BasicForm;
  },
  validate: async () => {
    return await formApi.validate();
  },
});

onMounted(async () => {
  await getPipelineTemplateOptions();
});
</script>

<template>
  <Form />
</template>
