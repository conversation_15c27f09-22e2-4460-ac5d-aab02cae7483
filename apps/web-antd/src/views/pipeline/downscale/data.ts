import type { FormData } from './type';

export const pipelineData = {
  stages: [
    {
      id: 'stage_id_1',
      name: '临时下线',
      is_enabled: true,
      status: 'READY',
      tasks: [
        {
          id: 'task_id_11',
          name: '临时下线',
          status: 'READY',
          is_enabled: true,
          subtasks: [
            {
              id: 'subtask_id_111',
              name: '多模块临时下线',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'region.host_temp_offline',
                input: [
                  {
                    name: '区服',
                    key: 'region',
                    value: 'region',
                    type: 'ref',
                  },
                  {
                    name: '缩容模块',
                    key: 'modules',
                    value: 'modules',
                    type: 'ref',
                  },
                ],
              },
            },
          ],
        },
        {
          id: 'task_id_12',
          name: '进程重载',
          status: 'READY',
          is_enabled: true,
          subtasks: [
            {
              id: 'subtask_id_121',
              name: '重载主机进程',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'hotwheel.exec_pipeline',
                input: [
                  {
                    name: '流程uid',
                    key: 'uid',
                    value: 'host_reload',
                    type: 'plain',
                  },
                  {
                    name: '流程名称',
                    key: 'name',
                    value: '重载主机进程',
                    type: 'plain',
                  },
                  {
                    name: '超时时间',
                    key: 'timeout',
                    value: 3,
                    type: 'plain',
                  },
                  {
                    name: '实例参数',
                    key: 'instance_params',
                    value: 'reload_hosts',
                    type: 'ref',
                  },
                ],
              },
            },
          ],
        },
      ],
    },
    {
      id: 'stage_id_2',
      name: '临时下线巡检',
      is_enabled: true,
      status: 'READY',
      tasks: [
        {
          id: 'task_id_21',
          name: '巡检',
          status: 'READY',
          is_enabled: true,
          subtasks: [
            {
              id: 'subtask_id_211',
              name: 't_server_list数据库巡检',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'sugar.checklist',
                input: [
                  {
                    name: '巡检模版ID',
                    key: 'id',
                    value: '6618b18804c3139b5d94dcbf',
                    type: 'plain',
                  },
                  {
                    name: 'kwargs',
                    key: 'kwargs',
                    value: {
                      key: 'value',
                    },
                    type: 'plain',
                  },
                ],
              },
            },
          ],
        },
      ],
    },
    {
      id: 'stage_id_3',
      name: '主机下线',
      is_enabled: true,
      status: 'READY',
      tasks: [
        {
          id: 'task_id_31',
          name: '主机下线',
          status: 'READY',
          is_enabled: true,
          subtasks: [
            {
              id: 'subtask_id_311',
              name: '多模块主机下线',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'region.host_offline',
                input: [
                  {
                    name: '区服',
                    key: 'region',
                    value: 'region',
                    type: 'ref',
                  },
                  {
                    name: '缩容模块',
                    key: 'modules',
                    value: 'modules',
                    type: 'ref',
                  },
                ],
              },
            },
          ],
        },
      ],
    },
    {
      id: 'stage_id_4',
      name: '主机下线巡检',
      is_enabled: true,
      status: 'READY',
      tasks: [
        {
          id: 'task_id_41',
          name: '巡检',
          status: 'READY',
          is_enabled: true,
          subtasks: [
            {
              id: 'subtask_id_411',
              name: 't_server_list数据库巡检',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'sugar.checklist',
                input: [
                  {
                    name: '巡检模版ID',
                    key: 'id',
                    value: '6618b18804c3139b5d94dcbf',
                    type: 'plain',
                  },
                  {
                    name: 'kwargs',
                    key: 'kwargs',
                    value: {
                      key: 'value',
                    },
                    type: 'plain',
                  },
                ],
              },
            },
            {
              id: 'subtask_id_412',
              name: 'CMDB模块巡检',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'sugar.checklist',
                input: [
                  {
                    name: '巡检模版ID',
                    key: 'id',
                    value: '6618b18804c3139b5d94dcbf',
                    type: 'plain',
                  },
                  {
                    name: 'kwargs',
                    key: 'kwargs',
                    value: {
                      key: 'value',
                    },
                    type: 'plain',
                  },
                ],
              },
            },
            {
              id: 'subtask_id_413',
              name: '监控平台机器数量巡检',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'sugar.checklist',
                input: [
                  {
                    name: '巡检模版ID',
                    key: 'id',
                    value: '6618b18804c3139b5d94dcbf',
                    type: 'plain',
                  },
                  {
                    name: 'kwargs',
                    key: 'kwargs',
                    value: {
                      key: 'value',
                    },
                    type: 'plain',
                  },
                ],
              },
            },
            {
              id: 'subtask_id_414',
              name: '阿里云应用分组数量巡检',
              is_enabled: true,
              status: 'READY',
              plugin: {
                name: 'sugar.checklist',
                input: [
                  {
                    name: '巡检模版ID',
                    key: 'id',
                    value: '6618b18804c3139b5d94dcbf',
                    type: 'plain',
                  },
                  {
                    name: 'kwargs',
                    key: 'kwargs',
                    value: {
                      key: 'value',
                    },
                    type: 'plain',
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};

// 表单数据
export const defaultFormData: FormData = {
  region: '',
  moduleHosts: [],
  reloadHosts: [],
};

export function getVariablesFromFormData(formData: FormData) {
  return [
    {
      name: '区服',
      key: 'region',
      type: 'plain',
      value: formData.region,
    },
    {
      name: '缩容模块',
      key: 'modules',
      type: 'plain',
      value: formData.moduleHosts,
    },
    {
      name: '重载机器',
      key: 'reload_hosts',
      type: 'plain',
      value: formData.reloadHosts,
    },
  ];
}

export const schemaJSON = `[
  {
    "component": "ApiSelect",
    "fieldName": "region",
    "label": "区服名称",
    "componentProps": {
      "placeholder": "请选择区服名称",
      "api": "() => {\\n            return requestClient.get('/regions/names');\\n          }",
      "resultField": "list",
      "immediate": true
    },
    "defaultValue": "",
    "rules": "required"
  },
  {
    "component": "ModuleSelection",
    "fieldName": "modules",
    "label": "缩容模块",
    "componentProps": {
      "showPatchId": false,
      "showProcessNum": false
    },
    "defaultValue": [],
    "dependencies": {
      "triggerFields": ["region"],
      "componentProps":
        "(values) => {\\n            if (!values.region) {\\n              return {};\\n            }\\n            return {\\n              region: values.region\\n            };\\n          }"
    },
    "rules": "required"
  },
  {
    "component": "RegionHostSelection",
    "fieldName": "reload_hosts",
    "label": "重载机器",
    "help": "() => {\\n          return h('div', null, [\\n            h('p', null, '\\u4E0A\\u7EBFgameserver\\u9700\\u8981\\u91CD\\u8F7Dgateserver'),\\n            h('p', null, '\\u4E0A\\u7EBFdispatch\\u9700\\u8981\\u91CD\\u8F7Dgateserver'),\\n            h('p', null, '\\u4E0A\\u7EBFnodeserver\\u9700\\u8981\\u91CD\\u8F7D\\u6240\\u6709\\u5176\\u4ED6\\u6A21\\u5757'),\\n            h('p', null, '\\u4E0A\\u7EBFfightmgr\\u9700\\u8981\\u91CD\\u8F7Dufight')\\n          ]);\\n        }",
    "defaultValue": [],
    "dependencies": {
      "triggerFields": ["region"],
      "componentProps": "(values) => {\\n            if (!values.region) {\\n              return {};\\n            }\\n            return {\\n              region: values.region\\n            };\\n          }"
    }
  }
]`;
