<script lang="ts" setup>
import { useVbenForm } from '@vben/common-ui';

const schema = [
  {
    component: 'Input',
    fieldName: 'region',
    label: '区服名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'branch',
    label: '默认分支',
    rules: 'required',
  },
  {
    component: 'RegionMysqlSelection',
    fieldName: 'mysql_infos',
    label: 'MySQL实例',
    rules: 'required',
    defaultValue: [],
    componentProps: {},
  },
  {
    component: 'RegionRedisSelection',
    fieldName: 'redis_infos',
    label: 'Redis实例',
    rules: 'required',
    defaultValue: [],
    componentProps: {},
  },
  {
    component: 'RadioGroup',
    fieldName: 'deploy_mode',
    label: '部署模式',
    rules: 'required',
    componentProps: {
      options: [
        { label: '单机部署', value: 'all_in_one' },
        { label: '分模块部署', value: 'deploy_by_modules' },
      ],
    },
  },
  {
    component: 'RegionHostSelection',
    fieldName: 'all_in_one_hosts',
    label: '单机部署',
    rules: 'required',
    dependencies: {
      triggerFields: ['deploy_mode'],
      if: (values: any) => values.deploy_mode === 'all_in_one',
    },
    defaultValue: [],
  },
  {
    component: 'ModuleSelection',
    fieldName: 'module_hosts',
    label: '分模块部署',
    rules: 'required',
    componentProps: {
      showPatchId: false,
    },
    dependencies: {
      triggerFields: ['deploy_mode', 'region'],
      if: (values: any) => values.deploy_mode === 'deploy_by_modules',
      componentProps: (values: any) => {
        return {
          region: values.region,
        };
      },
    },
    defaultValue: [
      {
        hosts: [],
        module: 'dispatch',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'muipserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'oaserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'nodeserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'gameserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'rankserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'gateserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'mailserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'snsserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'fightmgrserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'matchserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'dbgate',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'ufightserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'fightserver',
        process_num: 1,
      },
      {
        hosts: [],
        module: 'roomserver',
        process_num: 1,
      },
    ],
  },
];

// 使用useVbenForm创建表单;
const [Form] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema,
  // schema,
  showDefaultActions: false,
});
</script>

<template>
  <Form />
</template>
