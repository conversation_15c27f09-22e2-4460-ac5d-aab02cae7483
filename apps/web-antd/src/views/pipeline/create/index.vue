<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { But<PERSON>, Card, message, Steps } from 'ant-design-vue';

import { createPipelineApi } from '#/api/engine/engine';
import { DynamicForm } from '#/components/dynamicForm';
import { PipelineConfig } from '#/components/pipelineConfig';
import { GenerateDateStr } from '#/utils/date';

import { pipelineData, schemaJSON } from './data';

const router = useRouter();

const form = ref<InstanceType<typeof DynamicForm>>();
const pd = ref(cloneDeep(pipelineData));
const submit = async () => {
  const region = await form.value?.region();
  const formValues = await form.value?.values();
  const name = `${region}-创建区服-${GenerateDateStr(new Date())}`;
  const submitData = {
    stages: pd.value.stages,
    name,
    description: name,
    variables: formValues,
  };
  createPipelineApi({
    pipeline: submitData,
  }).then((data) => {
    message.success('创建任务成功');
    router.push({ name: 'pipeline_detail', params: { id: data.pipeline_id } });
  });
};

const current = ref<number>(0);
const next = async () => {
  if (current.value === 0) {
    const res = await form.value?.validate();
    if (!res?.valid) {
      return;
    }
  }
  current.value++;
};
const prev = () => {
  current.value--;
};
const steps = [
  {
    title: '填写参数',
  },
  {
    title: '编辑流水线',
  },
];
const items = steps.map((item) => ({ key: item.title, title: item.title }));
</script>

<template>
  <Page auto-content-height description="创建新区服" title="创建区服">
    <Card
      style="height: calc(100% - 50px)"
      :body-style="{ height: '100%', overflow: 'auto' }"
    >
      <Steps :current="current" :items="items" />
      <div v-show="current === 0" class="mt-12">
        <!-- <CreateForm :form-data="formData" /> -->
        <DynamicForm ref="form" :schema="schemaJSON" />
      </div>
      <div v-if="current === 1" class="mt-16">
        <PipelineConfig
          :pipeline-data="pd"
          @update:pipeline-data="pd = $event"
        />
      </div>
    </Card>
    <div class="mt-4 flex justify-end">
      <Button v-if="current < steps.length - 1" type="primary" @click="next">
        下一步
      </Button>
      <Button v-if="current > 0" @click="prev" class="mr-4">上一步</Button>
      <Button
        v-if="current === steps.length - 1"
        @click="submit"
        type="primary"
      >
        创建任务
      </Button>
    </div>
  </Page>
</template>
