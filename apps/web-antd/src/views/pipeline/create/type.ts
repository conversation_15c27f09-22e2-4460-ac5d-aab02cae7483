import type { HostInfo } from '#/components/hostSelection/type';

export interface MysqlInfo {
  instance_id: string;
  mysql_user: string;
  mysql_pwd: string;
  table_idx: string[];
}

export interface RedisInfo {
  instance_id: string;
  redis_pwd: string;
  idx: string[];
}
// 定义 formData 类型
export interface FormData {
  region: string;
  branch: string;
  mysqlInfos: MysqlInfo[];
  redisInfos: RedisInfo[];
  deployMode: string;
  allInOneHosts: HostInfo[];
  moduleHosts: { hosts: HostInfo[]; module: string; process_num: number }[];
}
