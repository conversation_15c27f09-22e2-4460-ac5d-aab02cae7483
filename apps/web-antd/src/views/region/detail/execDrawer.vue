<script lang="ts" setup>
import type { VbenFormSchema } from '#/adapter/form';
import type {
  RegionDetailInfo,
  Shortcut,
  ShortcutVariable,
} from '#/api/region/type';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { execRegionShortcutApi, getRegionShortcutVariableApi } from '#/api';
import { GenerateDateStr } from '#/utils/date';

const router = useRouter();

const region = ref<RegionDetailInfo>();
const shortcutData = ref<Shortcut>();
const shortcutVariables = ref<ShortcutVariable[]>();

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = drawerApi.getData<Record<string, any>>();
      shortcutData.value = data.shortcut_data;
      region.value = data.region;
      getForms();
    }
  },
});

const [Form, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 120,
  },
  handleSubmit: onSubmit,
  handleReset: async () => {
    await getForms();
  },
  schema: [],
});

const instanceKey = ref('');

function onSubmit(values: Record<string, any>) {
  drawerApi.lock();

  execRegionShortcutApi(
    region.value?.name || '',
    shortcutData.value?.uid || '',
    {
      pipeline_name: `${values.pipeline_name}-${GenerateDateStr(new Date())}`,
      timeout: 60,
      variables: Object.keys(values)
        .filter((key) => key !== 'auto_start' && key !== instanceKey.value)
        .map((key) => {
          return {
            name:
              shortcutVariables.value?.find((item) => item.key === key)
                ?.label || key,
            key,
            value: values[key],
          };
        }),
      instance_variables: {
        name: '实例参数',
        key: instanceKey.value.replaceAll(/[${}]/g, ''), // 去掉${}
        value: values[instanceKey.value],
      },
      auto_start: values.auto_start,
    },
  )
    .then((data) => {
      message.success('执行成功');
      router.push({
        name: 'region_pipeline_detail',
        params: { id: data.pipeline_id, name: region.value?.name },
      });
      drawerApi.close();
    })
    .finally(() => {
      drawerApi.unlock();
    });
}

const getForms = async () => {
  shortcutVariables.value = await getRegionShortcutVariableApi(
    region.value?.name || '',
    shortcutData.value?.uid || '',
  );
  const formValues: Record<string, any> = {};
  const newSchemas: VbenFormSchema[] = shortcutVariables.value?.map((item) => {
    let defaultValue: any = item.default_value;
    let component = 'Input';
    let componentProps: any = {
      class: 'w-full',
    };
    switch (item.key) {
      case `$\{branch}`: {
        defaultValue = region.value?.code_branch || '';
        break;
      }
      case `$\{data_branch}`: {
        defaultValue = region.value?.data_branch || '';
        break;
      }
      case `$\{patch_id}`: {
        component = 'RegionPatchSelection';
        defaultValue = undefined;
        componentProps = {
          class: 'w-full',
          region: region.value?.name,
          placeholder: '请选择',
        };
        break;
      }
      case `$\{region}`: {
        defaultValue = region.value?.name || '';
        break;
      }
      default: {
        break;
      }
    }
    if (item.type === 'instance') {
      component = 'RegionHostSelection';
      defaultValue = [];
      instanceKey.value = item.key;
      componentProps = {
        region: region.value?.name,
      };
      if (instanceKey.value === `$\{opHost}`) {
        componentProps.useJumpHosts = true;
      }
    }
    formValues[item.key] = defaultValue;
    return {
      component,
      fieldName: item.key,
      label: item.key,
      defaultValue,
      help: () => {
        return item.label;
      },
      componentProps,
      rules: item.is_required || item.type === 'instance' ? 'required' : '',
    };
  });
  newSchemas.push({
    component: 'Switch',
    fieldName: 'auto_start',
    label: '创建并启动',
    defaultValue: true,
  });
  newSchemas.unshift({
    component: 'Input',
    fieldName: 'pipeline_name',
    label: '流程名称',
    defaultValue: `${region.value?.name}-${shortcutData.value?.name}`,
    rules: 'required',
  });
  formApi.setState({
    schema: newSchemas,
  });
  formApi.setValues(formValues);
};
</script>
<template>
  <Drawer append-to-main :title="shortcutData?.name" class="w-[900px]">
    <Form v-if="shortcutVariables" />
  </Drawer>
</template>
