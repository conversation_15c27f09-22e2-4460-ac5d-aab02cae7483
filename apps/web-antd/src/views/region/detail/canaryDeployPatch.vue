<script lang="ts" setup>
import type { VbenFormSchema } from '#/adapter/form';
import type { RegionDetailInfo, ShortcutVariable } from '#/api/region/type';
import type { HostInfo } from '#/components/hostSelection/type';

import { ref, unref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  execRegionShortcutApi,
  getRegionHostsApi,
  getRegionShortcutVariableApi,
} from '#/api';
import { GenerateDateStr } from '#/utils/date';

const region = ref<RegionDetailInfo>();
const regionHosts = ref<HostInfo[]>([]);
const router = useRouter();
const instanceKey = ref('');
const shortcutVariables = ref<ShortcutVariable[]>();

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = drawerApi.getData<Record<string, any>>();
      region.value = data.region;
      getRegionHosts(region.value?.name || '').then(() => {
        getForms();
      });
    }
  },
});

const getRegionHosts = async (region: string) => {
  await getRegionHostsApi(region)
    .then((res) => {
      regionHosts.value = res.list;
    })
    .finally(() => {});
};

function onSubmit(values: Record<string, any>) {
  drawerApi.lock();
  execRegionShortcutApi(region.value?.name || '', 'update_patch', {
    pipeline_name: `${values.pipeline_name}-${GenerateDateStr(new Date())}`,
    timeout: 60,
    variables: Object.keys(values)
      .filter(
        (key) =>
          shortcutVariables.value?.map((item) => item.key).includes(key) &&
          key !== instanceKey.value,
      )
      .map((key) => {
        return {
          name:
            shortcutVariables.value?.find((item) => item.key === key)?.label ||
            key,
          key,
          value: values[key],
        };
      }),
    instance_variables: {
      name: '实例参数',
      key: instanceKey.value.replaceAll(/[${}]/g, ''), // 去掉${}
      value: values[instanceKey.value],
    },
    auto_start: values.auto_start,
    canary_option: {
      canary_mode: values.canary_mode,
      canary_batch_count: values.canary_count,
      custom_canary_hosts: values.canary_hosts,
      interval_mode: values.exec_mode,
      interval: values.exec_interval,
      op_host: values.op_host,
      has_hosts_value: false,
    },
  })
    .then((data) => {
      message.success('执行成功');
      router.push({
        name: 'region_pipeline_detail',
        params: { id: data.pipeline_id, name: region.value?.name },
      });
      drawerApi.close();
    })
    .finally(() => {
      drawerApi.unlock();
    });
}

const getForms = async () => {
  shortcutVariables.value = await getRegionShortcutVariableApi(
    region.value?.name || '',
    'update_patch',
  );

  const canarySchema = [
    {
      component: 'RadioGroup',
      componentProps: {
        placeholder: '请输入',
        options: [
          { label: '百分比', value: 'percent' },
          { label: '数量', value: 'count' },
          { label: '自定义', value: 'custom' },
        ],
      },
      fieldName: 'canary_mode',
      label: '分批模式',
      rules: 'required',
      defaultValue: 'percent',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入',
        min: 1,
        max: 100,
        addonAfter: '%',
      },
      fieldName: 'canary_percent',
      label: '分批百分比',
      rules: 'required',
      defaultValue: 20,
      dependencies: {
        triggerFields: ['canary_mode'],
        if: (values: any) => values.canary_mode === 'percent',
      },
    },
    {
      component: 'CanaryHost',
      fieldName: 'canary_hosts',
      label: '自定义分批机器',
      rules: 'required',
      componentProps: {
        hosts: unref(regionHosts),
        class: 'w-full',
      },
      dependencies: {
        triggerFields: ['canary_mode'],
        if: (values: any) => values.canary_mode === 'custom',
      },
      defaultValue: [],
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入',
        min: 1,
        addonAfter: '台',
      },
      fieldName: 'canary_count',
      label: '分批数量',
      rules: 'required',
      defaultValue: 10,
      dependencies: {
        triggerFields: ['canary_mode'],
        if: (values: any) => values.canary_mode === 'count',
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        placeholder: '请输入',
        options: [
          { label: '时间间隔', value: 'time' },
          { label: '手动继续', value: 'manual' },
        ],
      },
      fieldName: 'exec_mode',
      label: '每批次执行模式',
      rules: 'required',
      defaultValue: 'time',
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入',
        min: 1,
        addonAfter: '秒',
      },
      fieldName: 'exec_interval',
      label: '时间间隔',
      rules: 'required',
      defaultValue: 5,
      dependencies: {
        triggerFields: ['exec_mode'],
        if: (values: any) => values.exec_mode === 'time',
      },
    },
  ];

  const formValues: Record<string, any> = {};
  const newSchemas: VbenFormSchema[] = shortcutVariables.value.map((item) => {
    let defaultValue: any = item.default_value;
    let component = 'Input';
    let componentProps: any = {
      class: 'w-full',
    };
    let dependencies: any;
    switch (item.key) {
      case `$\{branch}`: {
        defaultValue = region.value?.code_branch || '';
        break;
      }
      case `$\{data_branch}`: {
        defaultValue = region.value?.data_branch || '';
        break;
      }
      case `$\{patch_id}`: {
        component = 'RegionPatchSelection';
        defaultValue = undefined;
        componentProps = {
          class: 'w-full',
          region: region.value?.name,
          placeholder: '请选择',
        };
        break;
      }
      case `$\{region}`: {
        defaultValue = region.value?.name || '';
        break;
      }
      default: {
        break;
      }
    }
    if (item.type === 'instance') {
      component = 'RegionHostSelection';
      defaultValue = [];
      instanceKey.value = item.key;
      componentProps = {
        region: region.value?.name,
      };
      if (instanceKey.value === `$\{opHost}`) {
        componentProps.useJumpHosts = true;
      }
      dependencies = {
        triggerFields: ['canary_mode'],
        if: (values: any) => values.canary_mode !== 'custom',
      };
    }
    formValues[item.key] = defaultValue;
    return {
      component,
      fieldName: item.key,
      label: item.key,
      defaultValue,
      help: () => {
        return item.label;
      },
      componentProps,
      rules: item.is_required || item.type === 'instance' ? 'required' : '',
      dependencies,
    };
  });
  newSchemas.push(...canarySchema, {
    component: 'Switch',
    fieldName: 'auto_start',
    label: '创建并启动',
    defaultValue: true,
  });
  formValues.canary_mode = 'custom';
  formValues.exec_mode = 'manual';
  newSchemas.unshift({
    component: 'Input',
    fieldName: 'pipeline_name',
    label: '流程名称',
    defaultValue: `${region.value?.name}-热更发布patch`,
    rules: 'required',
  });
  formApi.setState({
    schema: newSchemas,
  });
  formApi.setValues(formValues);
};

const [Form, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 120,
  },
  handleSubmit: onSubmit,
  handleReset: () => {
    getForms();
  },
  schema: [],
});
</script>

<template>
  <Drawer append-to-main title="热更发布patch" class="w-[1000px]">
    <Form v-if="instanceKey" />
  </Drawer>
</template>
