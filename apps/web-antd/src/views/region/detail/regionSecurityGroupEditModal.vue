<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import {
  getLbSecurityGroups,
  updateLbSecurityGroups,
} from '#/api/region/region';

defineOptions({
  name: 'RegionSecurityGroupEditModal',
});

const region = ref<string>('');

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'security_group_ids',
      label: '安全组',
      componentProps: {
        class: 'w-full',
        placeholder: '请选择安全组',
        api: () => {
          return getLbSecurityGroups(region.value);
        },
        showSearch: true,
        optionFilterProp: 'label',
        resultField: 'list',
        labelField: 'security_group_name',
        valueField: 'security_group_id',
        immediate: true,
        mode: 'multiple',
      },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      region.value = data.region;
      formApi.setValues({
        security_group_ids: data.security_group_ids,
      });
    }
  },
  title: '修改负载均衡安全组',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  const { lb_id, callback } = modalApi.getData();
  updateLbSecurityGroups(region.value, lb_id, {
    security_group_ids: values.security_group_ids,
  })
    .then(() => {
      modalApi.close();
      message.success({
        content: `修改安全组成功`,
        duration: 2,
      });
      callback();
    })
    .catch(() => {
      modalApi.unlock();
    });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
