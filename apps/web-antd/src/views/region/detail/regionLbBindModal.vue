<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getRegionLbs, updateRegionLb } from '#/api/region/region';

defineOptions({
  name: 'RegionLbBindModal',
});

const region = ref<string>('');

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'ApiSelect',
      fieldName: 'lb_instance_id',
      label: '负载均衡',
      componentProps: {
        class: 'w-full',
        placeholder: '请选择负载均衡实例',
        api: () => {
          return getRegionLbs(region.value);
        },
        showSearch: true,
        optionFilterProp: 'label',
        resultField: 'list',
        labelField: 'lb_name',
        valueField: 'lb_id',
        immediate: true,
      },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      region.value = data.region;
    }
  },
  title: '绑定区服负载均衡',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  updateRegionLb(region.value, {
    lb_id: values.lb_instance_id,
  })
    .then(() => {
      modalApi.close();
      message.success({
        content: `绑定负载均衡成功`,
        duration: 2,
      });
      const { callback } = modalApi.getData();
      callback();
    })
    .catch(() => {
      modalApi.unlock();
    });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
