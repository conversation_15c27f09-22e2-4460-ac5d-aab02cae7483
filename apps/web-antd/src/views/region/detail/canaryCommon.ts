export const canarySchema = [
  {
    component: 'RadioGroup',
    componentProps: {
      placeholder: '请输入',
      options: [
        { label: '百分比', value: 'percent' },
        { label: '数量', value: 'count' },
      ],
    },
    fieldName: 'canary_mode',
    label: '分批模式',
    rules: 'required',
    defaultValue: 'percent',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入',
      min: 1,
      max: 100,
      addonAfter: '%',
    },
    fieldName: 'canary_percent',
    label: '分批百分比',
    rules: 'required',
    defaultValue: 20,
    dependencies: {
      triggerFields: ['canary_mode'],
      if: (values: any) => values.canary_mode === 'percent',
    },
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入',
      min: 1,
      addonAfter: '台',
    },
    fieldName: 'canary_count',
    label: '分批数量',
    rules: 'required',
    defaultValue: 10,
    dependencies: {
      triggerFields: ['canary_mode'],
      if: (values: any) => values.canary_mode === 'count',
    },
  },
  {
    component: 'RadioGroup',
    componentProps: {
      placeholder: '请输入',
      options: [
        { label: '时间间隔', value: 'time' },
        { label: '手动继续', value: 'manual' },
      ],
    },
    fieldName: 'exec_mode',
    label: '每批次执行模式',
    rules: 'required',
    defaultValue: 'time',
  },
  {
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入',
      min: 1,
      addonAfter: '秒',
    },
    fieldName: 'exec_interval',
    label: '时间间隔',
    rules: 'required',
    defaultValue: 5,
    dependencies: {
      triggerFields: ['exec_mode'],
      if: (values: any) => values.exec_mode === 'time',
    },
  },
];
