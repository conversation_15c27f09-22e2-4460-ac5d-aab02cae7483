<script lang="ts" setup>
import type {
  RegionDetailInfo,
  RegionLbDetail,
  Shortcut,
  ShortcutCategory,
} from '#/api/region/type';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, prompt, useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { EditOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Col,
  Collapse,
  CollapsePanel,
  Descriptions,
  DescriptionsItem,
  Divider,
  message,
  Row,
  Switch,
  Tag,
  Tooltip,
} from 'ant-design-vue';

import {
  disableMonitorNotify,
  enableMonitorNotify,
  getRegionDetailApi,
  getRegionLbDetail,
  getRegionShortcutApi,
  lockRegionApi,
  unlockRegionApi,
  updateRegionLb,
} from '#/api';
import RegionHistory from '#/components/regionHistory/index.vue';
import RegionMetrics from '#/components/regionMetrics/index.vue';

import ListRegionHostInfoModal from '../list/listRegionHostInfoModal.vue';
import UpdateRegionModal from '../list/updateRegionModal.vue';
import CanaryDeployPatch from './canaryDeployPatch.vue';
import CanaryRestartDrawer from './canaryRestartDrawer.vue';
import ExecDrawer from './execDrawer.vue';
import RegionLbBindModal from './regionLbBindModal.vue';
import RegionSecurityGroupEditModal from './regionSecurityGroupEditModal.vue';

const [UpdateFormModal, updateFormModalApi] = useVbenModal({
  connectedComponent: UpdateRegionModal,
});

const router = useRouter();
const regionName = router.currentRoute.value.params.name as string;
const spinning = ref<boolean>(false);
const regionDetail = ref<RegionDetailInfo>({} as RegionDetailInfo);
const regionLbDetail = ref<RegionLbDetail>({
  lb_name: '',
  lb_id: '',
  security_group_ids: [],
} as RegionLbDetail);
const [ListModal, listRegionHostInfoModalApi] = useVbenModal({
  connectedComponent: ListRegionHostInfoModal,
});

const showhostInfo = (region: string, is_healthy: boolean) => {
  listRegionHostInfoModalApi.setData({ region, is_healthy }).open();
};

const activeKey = ref(['1']);
const monitorNotifyLoading = ref<boolean>(false);
const quickActions = ref<ShortcutCategory[]>();

const getQuickActionsData = async () => {
  quickActions.value = await getRegionShortcutApi(regionName);
};

const handleLock = () => {
  prompt({
    title: `锁定区服-${regionName}`,
    content: '请输入锁定原因',
  })
    .then(async (lock_reason: string) => {
      lockRegionApi(regionName, lock_reason).then(() => {
        message.success('锁定成功');
        getRegionDetail();
      });
    })
    .catch(() => {
      console.warn('cancelled');
    });
};

const handleUnlock = () => {
  unlockRegionApi(regionName).then(() => {
    message.success('解锁成功');
    getRegionDetail();
  });
};

const getRegionDetail = async (): Promise<void> => {
  regionDetail.value = await getRegionDetailApi(regionName);
  if (regionDetail.value.region_dp_lb?.lb_instance_id) {
    regionLbDetail.value = await getRegionLbDetail(
      regionName,
      regionDetail.value.region_dp_lb.lb_instance_id,
    );
  }
};

const handleEdit = (regionDetail: any) => {
  updateFormModalApi
    .setData({
      callback: getRegionDetail,
      ...regionDetail,
    })
    .open();
};

const [ExecDrawerDetail, execDrawerApi] = useVbenDrawer({
  connectedComponent: ExecDrawer,
  destroyOnClose: true,
});
const [CanaryRestartDrawerDetail, canaryRestartDrawerApi] = useVbenDrawer({
  connectedComponent: CanaryRestartDrawer,
  destroyOnClose: true,
});

const [RegionDpLbBindModal, regionLbBindModalApi] = useVbenModal({
  connectedComponent: RegionLbBindModal,
  destroyOnClose: true,
});

const [RegionLbSecurityGroupEditModal, regionSecurityGroupEditModalApi] =
  useVbenModal({
    connectedComponent: RegionSecurityGroupEditModal,
    destroyOnClose: true,
  });

const [CanaryDeployPatchDrawerDetail, canaryDeployPatchDrawerApi] =
  useVbenDrawer({
    connectedComponent: CanaryDeployPatch,
    destroyOnClose: true,
  });

const handleExec = (shortcut: Shortcut) => {
  if (shortcut.custom_component === 'canary_restart') {
    canaryRestartDrawerApi.setData({ region: regionDetail.value }).open();
    return;
  } else if (shortcut.custom_component === 'canary_deploy_patch') {
    canaryDeployPatchDrawerApi
      .setData({ region: regionDetail.value, shortcut_data: shortcut })
      .open();
    return;
  }

  execDrawerApi
    .setData({
      region: regionDetail.value,
      shortcut_data: shortcut,
    })
    .open();
};
const changeMonitorNotify = (value: boolean) => {
  monitorNotifyLoading.value = true;
  if (value) {
    enableMonitorNotify(regionName)
      .then(() => {
        message.success('开启成功');
        regionDetail.value.region_monitor_notify = true;
        monitorNotifyLoading.value = false;
      })
      .finally(() => {
        monitorNotifyLoading.value = false;
      });
  } else {
    disableMonitorNotify(regionName)
      .then(() => {
        message.success('关闭成功');
        regionDetail.value.region_monitor_notify = false;
        monitorNotifyLoading.value = false;
      })
      .finally(() => {
        monitorNotifyLoading.value = false;
      });
  }
};

const handleUnbindLb = () => {
  updateRegionLb(regionName, {
    lb_id: '',
  }).then(() => {
    message.success('解绑成功');
    getRegionDetail();
  });
};

const handleEditSecurityGroups = () => {
  regionSecurityGroupEditModalApi
    .setData({
      region: regionDetail.value.name,
      lb_id: regionLbDetail.value.lb_id,
      security_group_ids: regionLbDetail.value.security_group_ids,
      callback: getRegionDetail,
    })
    .open();
};

onMounted(async () => {
  spinning.value = true;
  await getRegionDetail();
  // await moveTabToGroup(regionDetail.value.name);
  await getQuickActionsData();
  spinning.value = false;
});
</script>
<template>
  <Page auto-content-height v-spinning="spinning">
    <UpdateFormModal />
    <Card>
      <template #title>
        {{ `${regionDetail.name} 区服详情` }}
        <Button @click="handleEdit(regionDetail)" type="text">
          <EditOutlined />
          编辑
        </Button>
      </template>
      <ListModal />
      <ExecDrawerDetail />
      <CanaryRestartDrawerDetail />
      <CanaryDeployPatchDrawerDetail />
      <RegionDpLbBindModal />
      <RegionLbSecurityGroupEditModal />
      <Descriptions bordered :column="4" size="small">
        <DescriptionsItem label="区服名称">
          {{ regionDetail.name }}
        </DescriptionsItem>
        <DescriptionsItem label="sdk环境">
          {{ regionDetail.region_extra_info?.sdk_env }}
        </DescriptionsItem>
        <DescriptionsItem label="分支配置">
          代码: {{ regionDetail.code_branch }} / 数值:
          {{ regionDetail.data_branch }}
        </DescriptionsItem>
        <DescriptionsItem label="运行分支">
          {{ regionDetail.region_extra_info?.monitor_branch }}
        </DescriptionsItem>
        <DescriptionsItem label="进程信息">
          <template
            v-if="
              regionDetail.region_extra_info?.region_process_info.is_healthy
            "
          >
            <Tag
              color="green"
              @click="showhostInfo(regionDetail.name, true)"
              style="cursor: pointer"
            >
              正常(
              {{
                `${regionDetail.region_extra_info?.region_process_info.healthy_count}/${regionDetail.region_extra_info?.region_process_info.healthy_count + regionDetail.region_extra_info?.region_process_info.un_healthy_count}`
              }}
              )
            </Tag>
          </template>
          <template v-else>
            <Tag
              color="error"
              @click="showhostInfo(regionDetail.name, false)"
              style="cursor: pointer"
            >
              异常(
              {{
                `${regionDetail.region_extra_info?.region_process_info.un_healthy_count}/${regionDetail.region_extra_info?.region_process_info.healthy_count + regionDetail.region_extra_info?.region_process_info.un_healthy_count}`
              }})
            </Tag>
          </template>
        </DescriptionsItem>
        <DescriptionsItem label="停服信息">
          <template
            v-if="
              !regionDetail.region_extra_info?.region_stop_info?.is_region_stop
            "
          >
            -
          </template>
          <template v-else>
            <Tooltip placement="top">
              <template #title>
                <p>
                  开始时间:
                  {{
                    regionDetail.region_extra_info.region_stop_info
                      .stop_begin_time
                  }}
                </p>
                <p>
                  结束时间:
                  {{
                    regionDetail.region_extra_info.region_stop_info
                      .stop_end_time
                  }}
                </p>
                <p>
                  原因:
                  {{ regionDetail.region_extra_info.region_stop_info.stop_msg }}
                </p>
              </template>
              <Tag color="error"> 已停服 </Tag>
            </Tooltip>
          </template>
        </DescriptionsItem>
        <DescriptionsItem label="对外状态">
          <template v-if="regionDetail.region_dp_lb?.lb_instance_id">
            <Tag
              color="green"
              v-if="regionLbDetail.security_group_ids.length === 0"
            >
              已对外
            </Tag>
            <Tag color="gray" v-else> 未对外 </Tag>
          </template>
          <template v-else> - </template>
        </DescriptionsItem>
        <DescriptionsItem label="DP负载均衡">
          <template v-if="regionDetail.region_dp_lb?.lb_instance_id">
            {{ regionLbDetail.lb_name }}
            <Button type="link" @click="handleUnbindLb">解绑</Button>
            <Button type="link" @click="handleEditSecurityGroups">
              修改安全组
            </Button>
          </template>
          <template v-else>
            未配置
            <Button
              type="link"
              @click="
                regionLbBindModalApi
                  .setData({
                    region: regionDetail.name,
                    callback: getRegionDetail,
                  })
                  .open()
              "
            >
              绑定
            </Button>
          </template>
        </DescriptionsItem>
        <DescriptionsItem label="锁定状态">
          <template v-if="!regionDetail.region_lock">
            <Switch
              :checked="false"
              @change="handleLock"
              un-checked-children="未锁定"
              checked-children="已锁定"
            />
          </template>
          <template v-else>
            <Tooltip placement="top">
              <template #title>
                原因: {{ regionDetail.region_lock.lock_reason }}
              </template>
              <Switch
                :checked="true"
                @change="handleUnlock"
                un-checked-children="未锁定"
                checked-children="已锁定"
              />
            </Tooltip>
          </template>
        </DescriptionsItem>
        <DescriptionsItem label="监控告警">
          <template v-if="regionDetail.region_monitor_notify">
            <Switch
              :checked="true"
              :loading="monitorNotifyLoading"
              @change="changeMonitorNotify(false)"
              un-checked-children="未开启"
              checked-children="已开启"
            />
          </template>
          <template v-else>
            <Switch
              :checked="false"
              :loading="monitorNotifyLoading"
              @change="changeMonitorNotify(true)"
              un-checked-children="未开启"
              checked-children="已开启"
            />
          </template>
        </DescriptionsItem>
        <DescriptionsItem label="占用情况" :span="1">
          <template v-if="!regionDetail.region_occupy"> - </template>
          <template v-else>
            {{ regionDetail.region_occupy.occupy_begin_time }} ~
            {{ regionDetail.region_occupy.occupy_end_time }}
            <br />
            {{ regionDetail.region_occupy.occupy_msg }}
          </template>
        </DescriptionsItem>
        <DescriptionsItem label="区服描述">
          {{ regionDetail.description ? regionDetail.description : '-' }}
        </DescriptionsItem>
      </Descriptions>
      <Collapse
        v-model:active-key="activeKey"
        destroy-inactive-tab-pane
        class="mt-4"
      >
        <CollapsePanel key="1" :show-arrow="false">
          <template #header> 快捷操作 </template>
          <template v-for="(item, index) in quickActions" :key="index">
            <Divider
              orientation="left"
              :style="{
                fontSize: '14px',
                marginTop: index === 0 ? '0' : '12px',
                marginBottom: '12px',
              }"
            >
              {{ item.name }}
            </Divider>
            <Row :gutter="16">
              <Col :span="2" v-for="action in item.shortcuts" :key="action.uid">
                <template v-if="action.desc">
                  <Tooltip class="w-full" placement="top">
                    <template #title> {{ action.desc }} </template>
                    <Button
                      class="w-full"
                      :disabled="regionDetail.region_lock !== null"
                      @click="handleExec(action)"
                    >
                      {{ action.name }}
                    </Button>
                  </Tooltip>
                </template>
                <template v-else>
                  <Button
                    class="w-full"
                    :disabled="regionDetail.region_lock !== null"
                    @click="handleExec(action)"
                  >
                    {{ action.name }}
                  </Button>
                </template>
              </Col>
            </Row>
          </template>
        </CollapsePanel>
      </Collapse>

      <RegionMetrics
        v-if="regionDetail.name"
        :region="regionDetail.name"
        class="mt-4"
      />
      <RegionHistory
        v-if="regionDetail.name"
        :region-name="regionDetail.name"
      />
    </Card>
  </Page>
</template>
