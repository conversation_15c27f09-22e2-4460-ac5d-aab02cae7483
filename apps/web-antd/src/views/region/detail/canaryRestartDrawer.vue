<script lang="ts" setup>
import type {
  MultiExecShortcutData,
  RegionDetailInfo,
} from '#/api/region/type';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { multiExecRegionShortcutApi } from '#/api';
import { GenerateDateStr } from '#/utils/date';

import { canarySchema } from './canaryCommon';

const region = ref<RegionDetailInfo>();
const router = useRouter();

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = drawerApi.getData<Record<string, any>>();
      region.value = data.region;
      formApi.setState({
        schema: getForms(),
      });
    }
  },
});

function onSubmit(values: Record<string, any>) {
  drawerApi.lock();
  const multiExecInfos: MultiExecShortcutData[] = values.modules.map(
    (item: any) => {
      if (item.hosts.length === 0) {
        return null;
      }
      let piplineUid = 'auto_restart';
      const taskName = `灰度重启模块 ${item.module}`;
      if (
        ['dispatch', 'globaldispatch', 'muipserver', 'oaserver'].includes(
          item.module,
        )
      ) {
        piplineUid = 'lb_server_restart';
      }

      return {
        instance_variables: {
          name: '实例参数',
          key: 'instance',
          value: item.hosts,
        },
        pipeline_uid: piplineUid,
        task_name: taskName,
        variables: [
          {
            name: '模块',
            key: `$\{module}`,
            value: item.module,
          },
          {
            name: 'server_type',
            key: `$\{server_type}`,
            value: item.module,
          },
          {
            name: '区服',
            key: `$\{region}`,
            value: region.value?.name,
          },
        ],
        canary_option: {
          canary_mode: values.canary_mode,
          canary_batch_count: values.canary_count,
          interval_mode: values.exec_mode,
          interval: values.exec_interval,
          op_host: values.op_host,
          has_hosts_value: true,
        },
      };
    },
  );

  multiExecRegionShortcutApi(region.value?.name || '', {
    pipeline_name: `${values.name}-${GenerateDateStr(new Date())}`,
    timeout: 300,
    auto_start: values.auto_start,
    multi_exec_infos: multiExecInfos,
  })
    .then((data) => {
      message.success('执行成功');
      router.push({
        name: 'region_pipeline_detail',
        params: { id: data.pipeline_id, name: region.value?.name },
      });
      drawerApi.close();
    })
    .finally(() => {
      drawerApi.unlock();
    });
}

const getForms = () => {
  const moduleProps = {
    region: region.value?.name,
    showPatchId: false,
    showProcessNum: false,
    class: 'w-full',
    enableModuleModification: true,
    showModeSelection: false,
    showCanaryQuickSelect: true,
  };
  const moduleDefaultValue = [];
  if (region.value?.name?.includes('_globaldispatch_')) {
    moduleProps.enableModuleModification = false;
    moduleDefaultValue.push({
      module: 'globaldispatch',
      hosts: [],
    });
  }

  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'name',
      label: '流程名称',
      rules: 'required',
      defaultValue: `${region.value?.name}-灰度重启`,
    },
    {
      component: 'ModuleSelection',
      fieldName: 'modules',
      label: '模块',
      rules: 'required',
      componentProps: moduleProps,
      defaultValue: moduleDefaultValue,
    },
    {
      component: 'RegionSimpleSelection',
      componentProps: {
        useJumpHosts: true,
        region: region.value?.name,
        class: 'w-full',
      },
      fieldName: 'op_host',
      label: '跳板机',
      rules: 'required',
      defaultValue: undefined,
    },
    ...canarySchema,
    {
      component: 'Switch',
      fieldName: 'auto_start',
      label: '创建并启动',
      defaultValue: true,
    },
  ];
};

const [Form, formApi] = useVbenForm({
  commonConfig: {
    labelWidth: 120,
  },
  handleSubmit: onSubmit,
  handleReset: () => {
    getForms();
  },
  schema: [],
});
</script>

<template>
  <Drawer append-to-main title="灰度重启" class="w-[1000px]">
    <Form />
  </Drawer>
</template>
