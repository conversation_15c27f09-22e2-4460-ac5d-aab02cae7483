<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { VersionTimelineDetailData } from '#/api/region/type';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Popconfirm } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteRegionTimelineApi,
  queryRegionTimelineApi,
} from '#/api/region/region';

import CreateRegionModal from './createModal.vue';
import UpdateRegionModal from './updateModal.vue';

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'version',
      label: '版本号',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: false,
  checkboxConfig: {
    highlight: true,
    labelField: 'version',
  },
  columns: [
    {
      title: '版本号',
      field: 'version',
    },
    {
      title: '版本描述',
      field: 'description',
    },
    {
      title: '开始时间',
      field: 'started_at',
    },
    {
      title: '结束时间',
      field: 'ended_at',
      formatter: ({ cellValue }) => {
        return cellValue || '-';
      },
    },
    {
      title: '创建人',
      field: 'created_by',
      visible: false,
    },
    {
      title: '更新人',
      field: 'updated_by',
    },
    {
      title: '创建时间',
      field: 'created_at',
      visible: false,
    },
    {
      title: '更新时间',
      field: 'updated_at',
    },
    {
      title: '操作',
      width: 260,
      field: 'actions',
      slots: { default: 'actions' },
    },
  ],
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryRegionTimelineApi({
          version: formValues.version,
          page: page.currentPage,
          size: page.pageSize,
        }).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const handleCreate = () => {
  createFormModalApi
    .setData({
      callback: gridApi.reload,
    })
    .open();
};

const handleEdit = (row: any) => {
  updateFormModalApi
    .setData({
      callback: gridApi.reload,
      ...row,
    })
    .open();
};

const [CreateFormModal, createFormModalApi] = useVbenModal({
  connectedComponent: CreateRegionModal,
});

const [UpdateFormModal, updateFormModalApi] = useVbenModal({
  connectedComponent: UpdateRegionModal,
});

const handleDelete = (row: VersionTimelineDetailData) => {
  deleteRegionTimelineApi(row.id).then(() => {
    message.success('删除成功');
    gridApi.reload();
  });
};
</script>

<template>
  <Page
    auto-content-height
    title="版本时间线"
    description="展示官服的版本时间线"
  >
    <CreateFormModal />
    <UpdateFormModal />
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="handleCreate">创建</Button>
      </template>
      <template #actions="{ row }">
        <Button @click="handleEdit(row)" class="mr-2" type="primary">
          编辑
        </Button>
        <Popconfirm
          ok-text="是"
          :title="`是否确认删除时间线 ${row.version}`"
          cancel-text="否"
          @confirm="handleDelete(row)"
        >
          <Button type="default" danger> 删除 </Button>
        </Popconfirm>
      </template>
    </Grid>
  </Page>
</template>
