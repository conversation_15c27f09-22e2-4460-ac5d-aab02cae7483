<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useVbenForm } from '#/adapter/form';
import { modifyRegionTimelineApi } from '#/api/region/region';

defineOptions({
  name: 'UpdateRegionModal',
});

const id = ref<number>(0);

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'version',
      label: '版本号',
      rules: 'required',
      disabled: true,
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'description',
      label: '描述',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请输入',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      fieldName: 'started_at',
      label: '开始时间',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请输入',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      fieldName: 'ended_at',
      label: '结束时间',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      formApi.setValues({
        version: data.version,
        description: data.description,
        started_at: dayjs(data.started_at as string),
        ended_at: data.ended_at ? dayjs(data.ended_at as string) : undefined,
      });
      id.value = data.id;
    }
  },
  title: '修改版本时间线表单',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  modifyRegionTimelineApi(id.value, {
    description: values.description,
    started_at: values.started_at
      ? (values.started_at as Dayjs).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
    ended_at: values.ended_at
      ? (values.ended_at as Dayjs).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
  }).then(() => {
    modalApi.close();
    message.success({
      content: `修改版本时间线${values.version}成功`,
      duration: 2,
    });
    const { callback } = modalApi.getData();
    callback();
  });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
