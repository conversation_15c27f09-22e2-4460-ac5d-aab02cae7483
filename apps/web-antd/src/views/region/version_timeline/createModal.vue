<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createRegionTimelineApi } from '#/api/region/region';

defineOptions({
  name: 'CreateModal',
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'version',
      label: '版本号',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'description',
      label: '描述',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请输入',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      fieldName: 'started_at',
      label: '开始时间',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请输入',
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      fieldName: 'ended_at',
      label: '结束时间',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  title: '创建版本时间线表单',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  createRegionTimelineApi({
    version: values.version,
    description: values.description,
    started_at: (values.started_at as Dayjs).format('YYYY-MM-DD HH:mm:ss'),
    ended_at: values.ended_at
      ? (values.ended_at as Dayjs).format('YYYY-MM-DD HH:mm:ss')
      : undefined,
  })
    .then(() => {
      modalApi.close();
      message.success({
        content: `创建版本时间线${values.version}成功`,
        duration: 2,
      });
      const { callback } = modalApi.getData();
      callback();
    })
    .catch(() => {
      modalApi.unlock();
    });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
