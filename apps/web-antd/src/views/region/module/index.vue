<script lang="ts" setup>
import type { MenuProps, TableColumnsType } from 'ant-design-vue';

import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Menu, Table, Tag } from 'ant-design-vue';

import { getRegionHostInfoApi, getRegionsNamesApi } from '#/api/region/region';

// 定义数据类型
interface RegionData {
  name: string;
  is_lock: boolean;
}

interface ModuleData {
  name: string;
  count: number;
}

interface HostData {
  hostname: string;
  ip: string;
  module: string;
  is_healthy: boolean;
  branch: string;
  data_version: string;
  patch_id: string;
  commit_id: string;
  built: string;
}

// 响应式数据
const regions = ref<RegionData[]>([]);
const modules = ref<ModuleData[]>([]);
const hosts = ref<HostData[]>([]);
const loading = ref(false);
const selectedRegion = ref<string>('');
const selectedModule = ref<string>('');

// 菜单选中的key
const selectedRegionKeys = ref<string[]>([]);
const selectedModuleKeys = ref<string[]>([]);

// 获取区服列表
const fetchRegions = async () => {
  try {
    const response = await getRegionsNamesApi();
    regions.value = response.list || [];
    if (regions.value.length > 0) {
      // 默认选中第一个区服
      selectedRegion.value = regions.value[0]!.name;
      selectedRegionKeys.value = [regions.value[0]!.name];
      await fetchModules();
    }
  } catch (error) {
    console.error('获取区服列表失败:', error);
  }
};

// 获取模块列表（基于选中的区服）
const fetchModules = async () => {
  if (!selectedRegion.value) return;

  try {
    loading.value = true;
    // 获取该区服下的主机信息来提取模块
    const response = await getRegionHostInfoApi(selectedRegion.value, {
      page: 1,
      size: 1000, // 获取所有数据来统计模块
    });

    // 统计各模块的主机数量
    const moduleMap = new Map<string, number>();
    response.list.forEach((host: HostData) => {
      const count = moduleMap.get(host.module) || 0;
      moduleMap.set(host.module, count + 1);
    });

    modules.value = [...moduleMap.entries()].map(([name, count]) => ({
      name,
      count,
    }));

    if (modules.value.length > 0) {
      // 默认选中第一个模块
      selectedModule.value = modules.value[0]!.name;
      selectedModuleKeys.value = [modules.value[0]!.name];
      await fetchHosts();
    } else {
      hosts.value = [];
    }
  } catch (error) {
    console.error('获取模块列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取主机列表（基于选中的区服和模块）
const fetchHosts = async () => {
  if (!selectedRegion.value) return;

  try {
    loading.value = true;
    const response = await getRegionHostInfoApi(selectedRegion.value, {
      page: 1,
      size: 1000,
    });

    // 如果选择了特定模块，则过滤主机
    hosts.value = selectedModule.value
      ? response.list.filter(
          (host: HostData) => host.module === selectedModule.value,
        )
      : response.list;
  } catch (error) {
    console.error('获取主机列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 区服菜单点击事件
const handleRegionSelect: MenuProps['onSelect'] = ({ key }) => {
  selectedRegion.value = key as string;
  selectedRegionKeys.value = [key as string];
  selectedModule.value = '';
  selectedModuleKeys.value = [];
  fetchModules();
};

// 模块菜单点击事件
const handleModuleSelect: MenuProps['onSelect'] = ({ key }) => {
  selectedModule.value = key as string;
  selectedModuleKeys.value = [key as string];
  fetchHosts();
};

// 区服菜单项
const regionMenuItems = computed(() =>
  regions.value.map((region) => ({
    key: region.name,
    label: region.name,
    disabled: region.is_lock,
  })),
);

// 模块菜单项
const moduleMenuItems = computed(() =>
  modules.value.map((module) => ({
    key: module.name,
    label: `${module.name} (${module.count})`,
  })),
);

// 表格列定义
const columns: TableColumnsType<HostData> = [
  {
    title: '主机名',
    dataIndex: 'hostname',
    key: 'hostname',
    width: 200,
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    key: 'ip',
    width: 150,
  },
  {
    title: '模块',
    dataIndex: 'module',
    key: 'module',
    width: 120,
  },
  {
    title: '健康状态',
    dataIndex: 'is_healthy',
    key: 'is_healthy',
    width: 100,
  },
  {
    title: '分支',
    dataIndex: 'branch',
    key: 'branch',
    width: 150,
  },
  {
    title: '数值版本',
    dataIndex: 'data_version',
    key: 'data_version',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      return text || '-';
    },
  },
  {
    title: 'patch_id',
    dataIndex: 'patch_id',
    key: 'patch_id',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      return text || '-';
    },
  },
  {
    title: 'Commit ID',
    dataIndex: 'commit_id',
    key: 'commit_id',
    width: 200,
    ellipsis: true,
  },
  {
    title: '构建时间',
    dataIndex: 'build_time',
    key: 'build_time',
    width: 180,
  },
];

// 组件挂载时获取数据
onMounted(() => {
  fetchRegions();
});
</script>

<template>
  <Page auto-content-height title="区服模块关系" description="展示区服模块关系">
    <div class="flex h-full gap-4">
      <!-- 左侧菜单区域 -->
      <div class="flex gap-4">
        <!-- 区服菜单 -->
        <div class="w-64 rounded-lg border bg-white">
          <div class="border-b p-4">
            <h3 class="text-lg font-medium text-gray-900">区服列表</h3>
            <p class="mt-1 text-sm text-gray-500">选择要查看的区服</p>
          </div>
          <div class="p-2">
            <Menu
              :items="regionMenuItems"
              :selected-keys="selectedRegionKeys"
              @select="handleRegionSelect"
              class="ant-menu-inline"
            />
          </div>
        </div>

        <!-- 模块菜单 -->
        <div class="w-64 rounded-lg border bg-white shadow-sm">
          <div class="border-b p-4">
            <h3 class="text-lg font-medium text-gray-900">模块列表</h3>
            <p class="mt-1 text-sm text-gray-500">
              {{ selectedRegion ? `${selectedRegion} 的模块` : '请先选择区服' }}
            </p>
          </div>
          <div class="p-2">
            <Menu
              v-if="modules.length > 0"
              :items="moduleMenuItems"
              :selected-keys="selectedModuleKeys"
              @select="handleModuleSelect"
              class="menu-no-border"
            />
            <div v-else class="p-4 text-center text-gray-500">
              {{ selectedRegion ? '该区服暂无模块' : '请先选择区服' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表格区域 -->
      <div class="flex-1 rounded-lg border bg-white shadow-sm">
        <div class="border-b p-4">
          <h3 class="text-lg font-medium text-gray-900">主机列表</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{
              selectedRegion && selectedModule
                ? `${selectedRegion} - ${selectedModule} 的主机信息`
                : selectedRegion
                  ? `${selectedRegion} 的所有主机信息`
                  : '请先选择区服和模块'
            }}
          </p>
        </div>
        <div class="p-4">
          <Table
            :columns="columns"
            :data-source="hosts"
            :loading="loading"
            :pagination="{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number, range: [number, number]) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }"
            :scroll="{ x: 1400 }"
            row-key="hostname"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'is_healthy'">
                <Tag :color="record.is_healthy ? 'green' : 'red'">
                  {{ record.is_healthy ? '健康' : '异常' }}
                </Tag>
              </template>
            </template>
          </Table>
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped>
.ant-menu-inline {
  border-right: none !important;
  margin: 0px;
}
</style>
