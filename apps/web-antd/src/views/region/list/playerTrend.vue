<script lang="ts" setup>
import type { EChartsOption, EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import dayjs from 'dayjs';

interface PlayerTrendData {
  player_num: number;
  timestamp: string;
}

const props = defineProps({
  data: {
    type: Array<PlayerTrendData>,
    required: true,
  },
});
const chartRef = ref<EchartsUIType>();

const { renderEcharts } = useEcharts(chartRef);

const getEchartOptions = () => {
  const option: EChartsOption = {
    xAxis: {
      type: 'category',
      show: false,
      data: props.data.map((item) => item.timestamp),
    },
    yAxis: { type: 'value', show: false },
    grid: {
      left: '2%',
      right: '2%',
      top: '25%',
      bottom: '25%',
    },
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        params = params[0];
        return `${dayjs(Number.parseInt(params.name, 10) * 1000).format('YYYY/MM/DD HH:mm:ss')} : ${params.value}`;
      },
      axisPointer: {
        animation: false,
      },
    },
    series: [
      {
        data: props.data.map((item) => item.player_num),
        type: 'line',
        smooth: true,
        symbol: 'circle',
      },
    ],
  };

  return option;
};

const renderChart = () => {
  renderEcharts(getEchartOptions());
};

onMounted(async () => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" style="height: 50px" />
</template>
