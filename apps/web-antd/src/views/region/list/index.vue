<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, prompt, useVbenModal } from '@vben/common-ui';

import { Button, message, Popconfirm, Tag, Tooltip } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteRegionApi,
  lockRegionApi,
  queryRegionsApi,
  unlockRegionApi,
} from '#/api/region/region';
import { queryRegionGroupsApi } from '#/api/region_group/region_group';

import CreateRegionModal from './createRegionModal.vue';
import ListRegionHostInfoModal from './listRegionHostInfoModal.vue';
import PlayerTrend from './playerTrend.vue';
import UpdateRegionModal from './updateRegionModal.vue';

const regionGroupOptions = ref([]);
const router = useRouter();

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '区服名称',
    },
    {
      component: 'Select',
      fieldName: 'region_group',
      label: '区服分组',
      componentProps: {
        allowClear: true,
        options: regionGroupOptions,
        placeholder: '全部',
        showSearch: true,
        mode: 'multiple',
      },
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: false,
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [
    {
      title: '区服名称',
      field: 'name',
    },
    {
      title: '数值分支',
      field: 'data_branch',
    },
    {
      title: '代码分支',
      field: 'code_branch',
    },
    {
      title: '运行分支',
      field: 'region_extra_info.monitor_branch',
    },
    {
      title: 'sdk环境',
      field: 'region_extra_info.sdk_env',
    },
    {
      title: '进程状态',
      field: 'process_info',
      slots: { default: 'process_info' },
    },
    {
      title: '停服状态',
      field: 'stop_info',
      slots: { default: 'stop_info' },
    },
    {
      title: '当前人数',
      field: 'user_count',
      formatter: ({ row }: { row: any }) => {
        return row.region_extra_info.region_process_info.player_trends
          .length === 0
          ? '未知'
          : row.region_extra_info.region_process_info.player_trends.slice(-1)[0]
              .player_num;
      },
    },
    {
      title: '人数趋势',
      field: 'player_trends',
      slots: { default: 'player_trend' },
    },
    {
      title: '占用情况',
      field: 'occupy_info',
      align: 'left',
      width: 300,
      slots: { default: 'occupy_info' },
    },
    {
      title: '锁定情况',
      field: 'region_lock',
      slots: { default: 'region_lock' },
    },
    {
      title: '区服描述',
      field: 'description',
      formatter: ({ row }: { row: any }) => {
        return row.description || '-';
      },
    },
    {
      title: '创建人',
      field: 'created_by',
      visible: false,
    },
    {
      title: '更新人',
      field: 'updated_by',
    },
    { title: '负责人', field: 'owner', visible: false },
    {
      title: '创建时间',
      field: 'created_at',
      visible: false,
    },
    {
      title: '更新时间',
      field: 'updated_at',
    },
    {
      title: '操作',
      width: 300,
      field: 'actions',
      slots: { default: 'actions' },
    },
  ],
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        await getRegionGroupsOptions();
        return await queryRegionsApi({
          name: formValues.name,
          region_group: formValues.region_group,
          page: page.currentPage,
          size: page.pageSize,
        }).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const getRegionGroupsOptions = async () => {
  const res = await queryRegionGroupsApi({ page: 1, size: 1000 });
  regionGroupOptions.value = res.list.map((item: any) => {
    return {
      label: item.name,
      value: item.code,
    };
  });
};

const handleCreate = () => {
  createFormModalApi
    .setData({
      callback: gridApi.reload,
    })
    .open();
};

const handleEdit = (row: any) => {
  updateFormModalApi
    .setData({
      callback: gridApi.reload,
      ...row,
    })
    .open();
};

const [CreateFormModal, createFormModalApi] = useVbenModal({
  connectedComponent: CreateRegionModal,
});

const [UpdateFormModal, updateFormModalApi] = useVbenModal({
  connectedComponent: UpdateRegionModal,
});

const [ListModal, listRegionHostInfoModalApi] = useVbenModal({
  connectedComponent: ListRegionHostInfoModal,
});

const handleView = (row: any) => {
  router.push({ name: 'regions_detail', params: { name: row.name } });
  // console.warn(row);
};

const handleDelete = (row: any) => {
  deleteRegionApi(row.name).then(() => {
    message.success('删除成功');
    gridApi.reload();
  });
};

const handleLock = (row: any) => {
  prompt({
    title: `锁定区服-${row.name}`,
    content: '请输入锁定原因',
  })
    .then(async (lock_reason: string) => {
      lockRegionApi(row.name, lock_reason).then(() => {
        message.success('锁定成功');
        gridApi.reload();
      });
    })
    .catch(() => {
      console.warn('cancelled');
    });
};

const handleUnlock = (row: any) => {
  unlockRegionApi(row.name).then(() => {
    message.success('解锁成功');
    gridApi.reload();
  });
};

const showhostInfo = (region: string, is_healthy: boolean) => {
  listRegionHostInfoModalApi.setData({ region, is_healthy }).open();
};
</script>

<template>
  <Page auto-content-height title="区服列表" description="展示所有区服">
    <CreateFormModal />
    <UpdateFormModal />
    <ListModal />
    <Grid>
      <template #process_info="{ row }">
        <template v-if="row.region_extra_info.region_process_info.is_healthy">
          <Tag
            color="green"
            @click="showhostInfo(row.name, true)"
            style="cursor: pointer"
          >
            正常(
            {{
              `${row.region_extra_info.region_process_info.healthy_count}/${row.region_extra_info.region_process_info.healthy_count + row.region_extra_info.region_process_info.un_healthy_count}`
            }}
            )
          </Tag>
        </template>
        <template v-else>
          <Tag
            color="error"
            @click="showhostInfo(row.name, false)"
            style="cursor: pointer"
          >
            异常(
            {{
              `${row.region_extra_info.region_process_info.un_healthy_count}/${row.region_extra_info.region_process_info.healthy_count + row.region_extra_info.region_process_info.un_healthy_count}`
            }})
          </Tag>
        </template>
      </template>
      <template #stop_info="{ row }">
        <template v-if="!row.region_extra_info.region_stop_info.is_region_stop">
          -
        </template>
        <template v-else>
          <Tooltip placement="top">
            <template #title>
              <p>
                开始时间:
                {{ row.region_extra_info.region_stop_info.stop_begin_time }}
              </p>
              <p>
                结束时间:
                {{ row.region_extra_info.region_stop_info.stop_end_time }}
              </p>
            </template>
            <Tag color="error"> 已停服 </Tag>
          </Tooltip>
        </template>
      </template>
      <template #occupy_info="{ row }">
        <div v-if="row.region_occupy">
          <p>{{ row.region_occupy.occupy_msg }}</p>
          <p>
            {{ row.region_occupy.occupy_begin_time }} ~
            {{ row.region_occupy.occupy_end_time }}
          </p>
        </div>
        <template v-else> - </template>
      </template>
      <template #region_lock="{ row }">
        <Tooltip placement="top" v-if="row.region_lock">
          <template #title> 原因: {{ row.region_lock.lock_reason }} </template>
          <Tag v-if="row.region_lock" color="error"> 已锁定 </Tag>
        </Tooltip>
        <template v-else> - </template>
      </template>
      <template #player_trend="{ row }">
        <div
          v-if="
            row.region_extra_info.region_process_info.player_trends.length === 0
          "
        >
          未知
        </div>
        <PlayerTrend
          v-else
          :data="row.region_extra_info.region_process_info.player_trends"
        />
      </template>
      <template #toolbar-tools>
        <Button type="primary" @click="handleCreate">创建</Button>
      </template>
      <template #actions="{ row }">
        <Button @click="handleView(row)" class="mr-2"> 查看 </Button>
        <Button
          @click="handleEdit(row)"
          class="mr-2"
          type="primary"
          :disabled="row.region_lock !== null"
        >
          编辑
        </Button>
        <span class="mr-2">
          <Popconfirm
            ok-text="是"
            :title="`是否确认解锁区服 ${row.name}`"
            cancel-text="否"
            @confirm="handleUnlock(row)"
            v-if="row.region_lock"
          >
            <Button>解锁</Button>
          </Popconfirm>
          <Button @click="handleLock(row)" v-else>锁定</Button>
        </span>
        <Popconfirm
          ok-text="是"
          :title="`是否确认删除区服 ${row.name}`"
          cancel-text="否"
          @confirm="handleDelete(row)"
        >
          <Button type="default" danger> 删除 </Button>
        </Popconfirm>
      </template>
    </Grid>
  </Page>
</template>
