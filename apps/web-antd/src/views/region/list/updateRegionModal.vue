<script lang="ts" setup>
import type { Dayjs } from 'dayjs';

import type { ModifyRegionOccupy } from '#/api/region/type';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenForm } from '#/adapter/form';
import { updateRegionApi } from '#/api/region/region';

defineOptions({
  name: 'UpdateRegionModal',
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'name',
      label: '名称',
      rules: 'required',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'data_branch',
      label: '数值分支',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'code_branch',
      label: '代码分支',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'owner',
      label: '负责人',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'description',
      label: '描述',
    },
    {
      component: 'RegionSelection',
      componentProps: {
        class: 'w-full',
        disableLockedRegion: false,
        excludeRegions: [],
      },
      dependencies: {
        triggerFields: ['name'],
        componentProps: (values: any) => {
          return {
            excludeRegions: [values.name],
          };
        },
      },
      fieldName: 'mirror_region',
      label: '镜像区服',
    },
    {
      component: 'Switch',
      componentProps: {},
      fieldName: 'region_occupy_enabled',
      label: '占用状态',
    },
    {
      component: 'DatePicker',
      dependencies: {
        triggerFields: ['region_occupy_enabled'],
        if: (values: any) => values.region_occupy_enabled,
      },
      componentProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: {
          format: 'HH:mm:ss',
          defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
        },
      },
      fieldName: 'region_occupy_begin',
      label: '占用开始时间',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      dependencies: {
        triggerFields: ['region_occupy_enabled'],
        if: (values: any) => values.region_occupy_enabled,
      },
      componentProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: {
          format: 'HH:mm:ss',
          defaultValue: dayjs('23:59:59', 'HH:mm:ss'),
        },
      },
      fieldName: 'region_occupy_end',
      label: '占用结束时间',
      rules: 'required',
    },
    {
      component: 'Textarea',
      dependencies: {
        triggerFields: ['region_occupy_enabled'],
        if: (values: any) => values.region_occupy_enabled,
      },
      fieldName: 'region_occupy_msg',
      label: '占用原因',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      const formValues = {
        ...data,
      };

      if (data.region_occupy) {
        formValues.region_occupy_enabled = true;
        formValues.region_occupy_begin = dayjs(
          data.region_occupy.occupy_begin_time,
        );
        formValues.region_occupy_end = dayjs(
          data.region_occupy.occupy_end_time,
        );
        formValues.region_occupy_msg = data.region_occupy.occupy_msg;
      } else {
        formValues.region_occupy_enabled = false;
      }
      formApi.setValues(formValues);
    }
  },
  title: '修改区服表单',
});

function onSubmit(values: Record<string, any>) {
  modalApi.lock();
  const regionOccupy: ModifyRegionOccupy = {
    is_enable: values.region_occupy_enabled,
  };
  if (values.region_occupy_enabled) {
    regionOccupy.occupy_begin_time = (
      values.region_occupy_begin as Dayjs
    ).format('YYYY-MM-DD HH:mm:ss');
    regionOccupy.occupy_end_time = (values.region_occupy_end as Dayjs).format(
      'YYYY-MM-DD HH:mm:ss',
    );
    regionOccupy.occupy_msg = values.region_occupy_msg;
  }

  updateRegionApi(values.name, {
    data_branch: values.data_branch,
    code_branch: values.code_branch,
    description: values.description,
    owner: values.owner,
    region_occupy: regionOccupy,
    mirror_region: values.mirror_region,
  }).then(() => {
    modalApi.close();
    message.success({
      content: `修改区服${values.name}成功`,
      duration: 2,
    });
    const { callback } = modalApi.getData();
    callback();
  });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
