<script lang="ts" setup>
import type { BasicForm } from './type';

import { useVbenForm } from '#/adapter/form';

const schema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '模板名称',
    rules: 'required',
  },
  {
    component: 'Input',
    fieldName: 'code',
    label: '模板代号',
    rules: 'required',
  },
  {
    component: 'Textarea',
    fieldName: 'desc',
    label: '模板描述',
    componentProps: {
      rows: 4,
    },
  },
  {
    component: 'NotificationConfig',
    fieldName: 'notification_config',
    label: '通知配置',
    defaultValue: {
      is_enabled: false,
      user_ids: [],
      group_ids: [],
    },
  },
];

const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema,
  showDefaultActions: false,
});

defineExpose({
  values: async () => {
    return (await formApi.getValues()) as BasicForm;
  },
  validate: async () => {
    return await formApi.validate();
  },
});
</script>

<template>
  <Form />
</template>
