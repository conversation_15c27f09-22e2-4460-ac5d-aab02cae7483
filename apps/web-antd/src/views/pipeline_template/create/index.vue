<script setup lang="ts">
import { ref, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { Button, Card, message, Steps } from 'ant-design-vue';

import { createPipelineTemplateApi } from '#/api/pipeline_template/template';
import { SchemaToJSON } from '#/components/dynamicForm/util';

import BasicInfoForm from './basicInfoForm.vue';
import FormEditor from './formEditor.vue';
import PipelineEditor from './pipelineEditor.vue';

const current = ref<number>(0);
const basicInfoForm =
  useTemplateRef<InstanceType<typeof BasicInfoForm>>('basicInfoForm');
const formEditor =
  useTemplateRef<InstanceType<typeof FormEditor>>('formEditor');
const pipelineEditor =
  useTemplateRef<InstanceType<typeof PipelineEditor>>('pipelineEditor');
const router = useRouter();

const prev = () => {
  current.value--;
};

const next = async () => {
  current.value++;
};

const steps = [
  {
    title: '基础信息',
  },
  {
    title: '流程表单',
  },
  {
    title: '流水线配置',
  },
];
const items = steps.map((item) => ({ key: item.title, title: item.title }));
const submit = async () => {
  const basicForm = (await basicInfoForm.value?.values()) || {
    name: '',
    desc: '',
    code: '',
    notification_config: {
      is_enable: false,
      user_ids: [],
      group_ids: [],
    },
  };
  const form = (await formEditor.value?.values()) || '[]';
  // eslint-disable-next-line no-eval
  const formJSON = SchemaToJSON(eval(form));
  const flow = await pipelineEditor.value?.values();

  await createPipelineTemplateApi({
    name: basicForm.name,
    code: basicForm.code,
    desc: basicForm.desc,
    notification_config: basicForm.notification_config,
    form: formJSON,
    flow,
  }).then((data) => {
    message.success(`创建流程模板${data.name}成功`);
    router.push({ name: 'pipeline_template_list' });
  });
};
</script>
<template>
  <Page
    auto-content-height
    title="创建流程模板"
    description="创建新的区服流程模板 "
  >
    <Card :body-style="{ height: '100%' }">
      <Steps :current="current" :items="items" />
      <div v-show="current === 0" class="mt-12">
        <BasicInfoForm ref="basicInfoForm" />
      </div>
      <div v-show="current === 1" class="mt-4">
        <FormEditor ref="formEditor" />
      </div>
      <div v-show="current === 2" class="mt-4">
        <PipelineEditor ref="pipelineEditor" />
      </div>
    </Card>
    <div class="mt-4 flex justify-end">
      <Button v-if="current > 0" @click="prev" class="mr-4">上一步</Button>
      <Button
        v-if="current < steps.length - 1"
        type="primary"
        @click="next"
        class="mr"
      >
        下一步
      </Button>
      <Button
        v-if="current === steps.length - 1"
        @click="submit"
        type="primary"
      >
        创建流程模板
      </Button>
    </div>
  </Page>
</template>
