<script setup lang="ts">
import type { VbenFormSchema } from '#/adapter/form';

import { h, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { requestClient } from '#/api/request';
import { MonacoEditor } from '#/components/monacoEditor';

const formSchema = ref<VbenFormSchema[]>();

const formSchemaCode = ref(`
[
  {
    component: 'Input',
    fieldName: 'field1',
    label: '标签1',
    rules: 'required',
  }
]
`);

const [Form, formApi] = useVbenForm(
  reactive({
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
    },
    schema: formSchema,
    showDefaultActions: false,
  }),
);

const executeDynamicCode = (codeToExecute: string) => {
  // 定义可以传递给动态代码的模块和变量
  const dependencies = {
    h,
    requestClient,
  };
  const dependencyKeys = Object.keys(dependencies);
  const dependencyValues = Object.values(dependencies);

  // eslint-disable-next-line no-new-func
  const dynamicFuncWrapper = new Function(
    ...dependencyKeys,
    `return (${codeToExecute})`,
  );
  const evaluatedResult = dynamicFuncWrapper(...dependencyValues);
  return evaluatedResult;
};

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  confirmText: '表单参数',
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.getValues().then((values) => {
      console.warn('debug:', values);
    });
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const schema = executeDynamicCode(
        formSchemaCode.value,
      ) as VbenFormSchema[];
      formSchema.value = schema;
      formApi.setValues(
        // eslint-disable-next-line unicorn/no-array-reduce
        schema.reduce((acc, item) => {
          acc[item.fieldName] = item.defaultValue || '';
          return acc;
        }, {} as any),
      );
    }
  },
  title: '预览表单',
});

const activeKey = ref('2');
defineExpose({
  values: async () => {
    return formSchemaCode.value;
  },
  validate: async () => {
    // TODO: add validation
    return true;
  },
});
</script>
<template>
  <Tabs v-model:active-key="activeKey" destroy-inactive-tab-pane>
    <TabPane key="1" tab="可视化编辑" disabled />
    <TabPane key="2" tab="代码编辑">
      <Button type="primary" @click="modalApi.open()"> 表单预览 </Button>
      <MonacoEditor
        class="mt-4"
        language="javascript"
        v-model:value="formSchemaCode"
        style="height: 760px"
      />
      <Modal class="w-[1000px]">
        <Form />
      </Modal>
    </TabPane>
  </Tabs>
</template>
L
