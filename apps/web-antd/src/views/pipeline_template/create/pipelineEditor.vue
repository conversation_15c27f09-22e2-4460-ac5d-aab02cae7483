<script setup lang="ts">
import { computed, ref } from 'vue';

import { TabPane, Tabs } from 'ant-design-vue';

import { MonacoEditor } from '#/components/monacoEditor';
import { PipelineEditor } from '#/components/pipelineEditor';

const pipelineData = ref({
  stages: [],
});

const activeKey = ref('1');

const pipelineDataCode = computed({
  get() {
    return JSON.stringify(pipelineData.value, null, 2);
  },
  set(value) {
    try {
      pipelineData.value = JSON.parse(value);
    } catch (error) {
      console.error(error);
    }
  },
});
defineExpose({
  values: async () => {
    return pipelineDataCode.value;
  },
  validate: async () => {
    // TODO: add validation
    return true;
  },
});
</script>

<template>
  <Tabs v-model:active-key="activeKey" destroy-inactive-tab-pane>
    <TabPane key="1" tab="可视化编辑">
      <PipelineEditor v-model:pipeline-data="pipelineData" />
    </TabPane>
    <TabPane key="2" tab="代码编辑">
      <MonacoEditor
        v-model:value="pipelineDataCode"
        style="height: calc(100vh - 500px)"
      />
    </TabPane>
  </Tabs>
</template>
