<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { PipelineTemplateDetail } from '#/api/pipeline_template/type';

import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { Button, message, Popconfirm } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deletePipelineTemplateApi,
  queryPipelineTemplateApi,
} from '#/api/pipeline_template/template';

const router = useRouter();

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '模板名称',
    },
    {
      component: 'Input',
      fieldName: 'description',
      label: '模板描述',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: false,
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [
    {
      title: '模板名称',
      field: 'name',
    },
    {
      title: '模板代号',
      field: 'code',
    },
    {
      title: '模板描述',
      field: 'desc',
      formatter: ({ row }: { row: any }) => {
        return row.desc || '-';
      },
    },
    {
      title: '创建人',
      field: 'created_by',
    },
    {
      title: '更新人',
      field: 'updated_by',
    },
    {
      title: '创建时间',
      field: 'created_at',
    },
    {
      title: '更新时间',
      field: 'updated_at',
    },
    { title: '操作', field: 'actions', slots: { default: 'actions' } },
  ],
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryPipelineTemplateApi(
          formValues.name,
          formValues.region_group,
          page.currentPage,
          page.pageSize,
        ).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const handleEdit = (row: PipelineTemplateDetail) => {
  router.push({ name: 'pipeline_template_edit', params: { code: row.code } });
};

const handleDelete = (row: PipelineTemplateDetail) => {
  deletePipelineTemplateApi(row.code).then(() => {
    message.success('删除成功');
    gridApi.reload();
  });
};

const handleCreate = () => {
  router.push({ name: 'pipeline_template_create' });
};

const handleExec = (row: PipelineTemplateDetail) => {
  router.push({ name: 'pipeline_template_exec', params: { code: row.code } });
};
</script>

<template>
  <Page
    auto-content-height
    title="流水线模板列表"
    description="展示所有流水线模板"
  >
    <Grid>
      <template #toolbar-tools>
        <Button type="primary" @click="handleCreate">创建</Button>
      </template>
      <template #actions="{ row }">
        <Button type="primary" @click="handleExec(row)" class="mr-2">
          执行
        </Button>
        <Button @click="handleEdit(row)" class="mr-2"> 编辑 </Button>
        <Popconfirm
          ok-text="是"
          :title="`是否确认删除模板 ${row.name}`"
          cancel-text="否"
          @confirm="handleDelete(row)"
        >
          <Button danger> 删除 </Button>
        </Popconfirm>
      </template>
    </Grid>
  </Page>
</template>
