<script lang="ts" setup>
import type { BasicForm } from './type';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '#/adapter/form';

const props = defineProps({
  originalData: {
    type: Object,
    required: false,
    default: () => ({}),
  },
});

const spinning = ref(false);

const schema = [
  {
    component: 'Input',
    fieldName: 'name',
    label: '模板名称',
    rules: 'required',
    disabled: true,
  },
  {
    component: 'Input',
    fieldName: 'code',
    label: '模板代号',
    rules: 'required',
    disabled: true,
  },
  {
    component: 'Textarea',
    fieldName: 'desc',
    label: '模板描述',
    componentProps: {
      rows: 4,
    },
  },
  {
    component: 'NotificationConfig',
    fieldName: 'notification_config',
    label: '通知配置',
    defaultValue: {},
  },
];

const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema,
  showDefaultActions: false,
});

defineExpose({
  values: async () => {
    return (await formApi.getValues()) as BasicForm;
  },
  validate: async () => {
    return await formApi.validate();
  },
});

onMounted(() => {
  spinning.value = true;
  const notificationConfig = props.originalData.notification_config;
  const safeConfig = {
    ...notificationConfig,
    user_ids: [...(notificationConfig.user_ids || [])],
    chat_ids: [...(notificationConfig.chat_ids || [])],
  };

  formApi.setValues({
    name: props.originalData.name,
    code: props.originalData.code,
    desc: props.originalData.desc,
    notification_config: safeConfig,
  });
  spinning.value = false;
});
</script>

<template>
  <Form v-spinning="spinning" />
</template>
