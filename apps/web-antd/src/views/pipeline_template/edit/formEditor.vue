<script setup lang="ts">
import type { VbenFormSchema } from '#/adapter/form';

import { h, onMounted, reactive, ref, watch } from 'vue';

import { TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { requestClient } from '#/api/request';
import { JSONToSchema } from '#/components/dynamicForm/util';
import { MonacoEditor } from '#/components/monacoEditor';

const props = defineProps({
  originalData: {
    type: Object,
    required: false,
    default: () => ({}),
  },
});

const formSchema = ref<VbenFormSchema[]>();

const formSchemaCode = ref(`
[
  {
    component: 'Input',
    fieldName: 'field1',
    label: '标签1',
    rules: 'required',
  }
]
`);

const [Form, formApi] = useVbenForm(
  reactive({
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
    },
    schema: formSchema,
    resetButtonOptions: {
      show: false,
    },
    submitButtonOptions: {
      content: '预览参数',
    },
    handleSubmit: async () => {
      await formApi.getValues().then((values) => {
        console.warn('debug:', values);
      });
    },
  }),
);

const activeKey = ref('2');
defineExpose({
  values: async () => {
    return formSchemaCode.value;
  },
  setValues: (form: string) => {
    const schema = JSONToSchema(form);
    formSchemaCode.value = objectToJsString(schema);
  },
  validate: async () => {
    // TODO: add validation
    return true;
  },
});

const objectToJsString = (obj: any, indent: number = 2) => {
  const visited = new WeakSet(); // 用于处理循环引用

  function _convert(o: any, currentIndent: number) {
    if (o === null) return 'null';
    if (o === undefined) return 'undefined';
    if (typeof o === 'string') return `'${o.replaceAll("'", String.raw`\'`)}'`;
    if (typeof o !== 'object') return o.toString(); // For number, boolean

    if (visited.has(o)) {
      return "'[Circular Reference]'";
    }
    visited.add(o);

    const nextIndent = currentIndent + indent;
    const indentStr = ' '.repeat(currentIndent);
    const nextIndentStr = ' '.repeat(nextIndent);

    if (Array.isArray(o)) {
      if (o.length === 0) return '[]';
      const items: any = o.map(
        (item) => `${nextIndentStr}${_convert(item, nextIndent)}`,
      );
      return `[\n${items.join(',\n')}\n${indentStr}]`;
    }

    const keys = Object.keys(o);
    if (keys.length === 0) return '{}';

    // 特殊处理函数对象，直接输出其代码
    if (typeof o === 'function') {
      return o.toString();
    }

    const properties: any = keys.map((key) => {
      const value = o[key];
      // 如果值的类型是函数，直接使用 toString()
      if (typeof value === 'function') {
        return `${nextIndentStr}${key}: ${value.toString()}`;
      }
      return `${nextIndentStr}${key}: ${_convert(value, nextIndent)}`;
    });

    return `{\n${properties.join(',\n')}\n${indentStr}}`;
  }

  return _convert(obj, 0);
};

watch(activeKey, () => {
  if (activeKey.value === '3') {
    const schema = executeDynamicCode(formSchemaCode.value) as VbenFormSchema[];
    formSchema.value = schema;
    formApi.setValues(
      // eslint-disable-next-line unicorn/no-array-reduce
      schema.reduce((acc, item) => {
        acc[item.fieldName] = item.defaultValue || '';
        return acc;
      }, {} as any),
    );
  }
});

const executeDynamicCode = (codeToExecute: string) => {
  // 定义可以传递给动态代码的模块和变量
  const dependencies = {
    h,
    requestClient,
  };
  const dependencyKeys = Object.keys(dependencies);
  const dependencyValues = Object.values(dependencies);

  // eslint-disable-next-line no-new-func
  const dynamicFuncWrapper = new Function(
    ...dependencyKeys,
    `return (${codeToExecute})`,
  );
  const evaluatedResult = dynamicFuncWrapper(...dependencyValues);
  return evaluatedResult;
};

onMounted(() => {
  const schema = JSONToSchema(props.originalData.form);
  formSchemaCode.value = objectToJsString(schema);
});
</script>
<template>
  <Tabs v-model:active-key="activeKey" destroy-inactive-tab-pane>
    <TabPane key="1" tab="可视化编辑" disabled />
    <TabPane key="2" tab="代码编辑">
      <MonacoEditor
        language="javascript"
        v-model:value="formSchemaCode"
        style="height: calc(100vh - 500px)"
      />
    </TabPane>
    <TabPane key="3" tab="表单预览">
      <Form />
    </TabPane>
  </Tabs>
</template>
L
