<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

import { TabPane, Tabs } from 'ant-design-vue';

import { MonacoEditor } from '#/components/monacoEditor';
import { PipelineEditor } from '#/components/pipelineEditor';

const props = defineProps({
  originalData: {
    type: Object,
    required: false,
    default: () => ({}),
  },
});

const pipelineData = ref({
  stages: [],
});

const activeKey = ref('1');

const pipelineDataCode = computed({
  get() {
    return JSON.stringify(pipelineData.value, null, 2);
  },
  set(value) {
    try {
      pipelineData.value = JSON.parse(value);
    } catch (error) {
      console.error(error);
    }
  },
});
defineExpose({
  values: async () => {
    return pipelineDataCode.value;
  },
  setValues: (flow: string) => {
    pipelineDataCode.value = flow;
  },
  validate: async () => {
    // TODO: add validation
    return true;
  },
});

onMounted(() => {
  pipelineDataCode.value = props.originalData.flow;
});
</script>

<template>
  <Tabs v-model:active-key="activeKey" destroy-inactive-tab-pane>
    <TabPane key="1" tab="可视化编辑">
      <PipelineEditor v-model:pipeline-data="pipelineData" />
    </TabPane>
    <TabPane key="2" tab="代码编辑">
      <MonacoEditor
        v-model:value="pipelineDataCode"
        style="height: calc(100vh - 500px)"
      />
    </TabPane>
  </Tabs>
</template>
