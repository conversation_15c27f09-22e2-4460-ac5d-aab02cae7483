<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { queryPipelineTemplateVersionsApi } from '#/api/pipeline_template/template';

const templateCode = ref<string>('');
const callback = ref<Function>();

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  schema: [
    {
      component: 'Input',
      fieldName: 'desc',
      label: '版本描述',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  showConfirmButton: false,
  cancelText: '关闭',
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      templateCode.value = data.templateCode;
      callback.value = data.callback;
    }
  },
  title: '流程模板历史',
  class: 'w-[900px]',
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    {
      title: '版本号',
      field: 'version',
    },
    {
      title: '版本描述',
      field: 'version_desc',
    },
    {
      title: '修改人',
      field: 'updated_by',
    },
    {
      title: '修改时间',
      field: 'updated_at',
    },
    {
      title: '操作',
      field: 'actions',
      slots: { default: 'actions' },
    },
  ],
  data: [],
  minHeight: 500,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryPipelineTemplateVersionsApi(
          templateCode.value,
          page.currentPage,
          page.pageSize,
          formValues.desc,
        ).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
};

const handleApply = (row: any) => {
  callback.value?.({
    flow: row.flow,
    form: row.form,
  });
};

const [Grid] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});
</script>
<template>
  <Modal>
    <Grid>
      <template #actions="{ row }">
        <Button type="primary" @click="handleApply(row)"> 应用 </Button>
      </template>
    </Grid>
  </Modal>
</template>
