<script setup lang="ts">
import type { PipelineTemplateDetail } from '#/api/pipeline_template/type';

import { onMounted, ref, useTemplateRef } from 'vue';
import { useRouter } from 'vue-router';

import { Page, prompt, useVbenModal } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { Button, Card, message, Steps } from 'ant-design-vue';

import {
  getPipelineTemplateApi,
  updatePipelineTemplateApi,
} from '#/api/pipeline_template/template';
import { SchemaToJSON } from '#/components/dynamicForm/util';

import BasicInfoForm from './basicInfoForm.vue';
import FormEditor from './formEditor.vue';
import PipelineEditor from './pipelineEditor.vue';
import VersionHistoryModal from './versionHistoryModal.vue';

const { closeCurrentTab } = useTabs();
const current = ref<number>(0);
const basicInfoForm =
  useTemplateRef<InstanceType<typeof BasicInfoForm>>('basicInfoForm');
const formEditor =
  useTemplateRef<InstanceType<typeof FormEditor>>('formEditor');
const pipelineEditor =
  useTemplateRef<InstanceType<typeof PipelineEditor>>('pipelineEditor');
const router = useRouter();
const templateCode = router.currentRoute.value.params.code as string;

const [VersionsModal, versionsModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: VersionHistoryModal,
});

const prev = () => {
  current.value--;
};

const next = async () => {
  current.value++;
};

const steps = [
  {
    title: '基础信息',
  },
  {
    title: '流程表单',
  },
  {
    title: '流水线配置',
  },
];
const items = steps.map((item) => ({ key: item.title, title: item.title }));
const originalData = ref<PipelineTemplateDetail>();
const originalDataLoading = ref(true);
const submit = async () => {
  console.warn('submit');
  const basicForm = (await basicInfoForm.value?.values()) || {
    name: '',
    desc: '',
    notification_config: {
      is_enabled: false,
      user_ids: [],
      group_ids: [],
    },
  };
  const form = (await formEditor.value?.values()) || '[]';
  // eslint-disable-next-line no-eval
  const formJSON = SchemaToJSON(eval(form));
  const flow = await pipelineEditor.value?.values();
  prompt({
    title: '保存流程模板',
    content: '请输入版本描述',
  })
    .then(async (version_desc: string) => {
      await updatePipelineTemplateApi(templateCode, {
        desc: basicForm.desc,
        notification_config: basicForm.notification_config,
        form: formJSON,
        flow,
        version_desc,
      }).then(() => {
        message.success(`编辑流程模板成功`);
        closeCurrentTab();
        router.push({ name: 'pipeline_template_list' });
      });
    })
    .catch(() => {
      console.warn('cancelled');
    });
};

const getOriginalData = async () => {
  originalDataLoading.value = true;
  await getPipelineTemplateApi(templateCode).then((data) => {
    originalDataLoading.value = false;
    originalData.value = data;
  });
};

const handleVersionHistory = () => {
  versionsModalApi
    .setData({ templateCode, callback: updateOriginalData })
    .open();
};

const updateOriginalData = (updateValue: any) => {
  if (!formEditor.value) {
    return;
  }
  formEditor.value.setValues(updateValue.form);
  if (!pipelineEditor.value) {
    return;
  }
  pipelineEditor.value.setValues(updateValue.flow);
  message.success('应用成功');
};

onMounted(async () => {
  await getOriginalData();
});
</script>
<template>
  <Page
    auto-content-height
    title="编辑流程模板"
    description="编辑已有的区服流程模板"
    v-spinning="originalDataLoading"
  >
    <template #extra>
      <VersionsModal />
      <Button @click="handleVersionHistory"> 版本历史 </Button>
    </template>
    <Card :body-style="{ height: 'calc(100vh - 320px)' }" v-if="originalData">
      <Steps :current="current" :items="items" />
      <div v-show="current === 0" class="mt-12">
        <BasicInfoForm ref="basicInfoForm" :original-data="originalData" />
      </div>
      <div v-show="current === 1" class="mt-4">
        <FormEditor ref="formEditor" :original-data="originalData" />
      </div>
      <div v-show="current === 2" class="mt-4">
        <PipelineEditor ref="pipelineEditor" :original-data="originalData" />
      </div>
    </Card>
    <div class="mt-4 flex justify-end">
      <Button v-if="current > 0" @click="prev" class="mr-4">上一步</Button>
      <Button
        v-if="current < steps.length - 1"
        type="primary"
        @click="next"
        class="mr"
      >
        下一步
      </Button>
      <Button
        v-if="current === steps.length - 1"
        @click="submit"
        type="primary"
      >
        保存
      </Button>
    </div>
  </Page>
</template>
