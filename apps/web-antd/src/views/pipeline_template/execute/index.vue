<script lang="ts" setup>
import type { PipelineTemplateDetail } from '#/api/pipeline_template/type';

import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { Button, Card, message, Steps } from 'ant-design-vue';

import { createPipelineApi } from '#/api';
import { getPipelineTemplateApi } from '#/api/pipeline_template/template';
import { DynamicForm } from '#/components/dynamicForm';
import { PipelineConfig } from '#/components/pipelineConfig';
import { GenerateDateStr } from '#/utils/date';

const { setTabTitle } = useTabs();
const router = useRouter();
// 优先使用路由meta的code，其次使用路由参数的code
const templateCode =
  (router.currentRoute.value.meta?.params &&
    (router.currentRoute.value.meta.params as any).code) ||
  (router.currentRoute.value.params.code as string);

const templateData = ref<PipelineTemplateDetail>();
const templateDataLoading = ref<boolean>(true);
const pipelineData = ref({
  stages: [],
});

const getPipelineTemplateDetail = async () => {
  templateDataLoading.value = true;
  await getPipelineTemplateApi(templateCode).then((data) => {
    templateDataLoading.value = false;
    pipelineData.value = JSON.parse(data.flow);
    templateData.value = data;
    setTabTitle(data.name);
  });
};

const schemaJSON = computed(() => {
  return templateData.value?.form;
});

const form = ref<InstanceType<typeof DynamicForm>>();
const submit = async () => {
  const region = await form.value?.region();
  const formValues = await form.value?.values();
  const name = `${region ?? '未知区服'}-${templateData.value?.name}-${GenerateDateStr(new Date())}`;
  const submitData = {
    stages: pipelineData.value.stages,
    name,
    description: name,
    variables: formValues,
    pipeline_template_id: templateData.value?.id,
    notification_config: templateData.value?.notification_config,
  };
  createPipelineApi({
    pipeline: submitData,
  }).then((data) => {
    message.success('创建任务成功');
    router.push({ name: 'pipeline_detail', params: { id: data.pipeline_id } });
  });
};

const current = ref<number>(0);
const next = async () => {
  if (current.value === 0) {
    const res = await form.value?.validate();
    if (!res?.valid) {
      return;
    }
  }
  current.value++;
};
const prev = () => {
  current.value--;
};
const steps = [
  {
    title: '填写参数',
  },
  {
    title: '编辑流水线',
  },
];
const items = steps.map((item) => ({ key: item.title, title: item.title }));

onMounted(async () => {
  await getPipelineTemplateDetail();
});
</script>
<template>
  <Page
    auto-content-height
    :title="templateData?.name"
    :description="templateData?.desc"
    v-spinning="templateDataLoading"
  >
    <Card
      style="height: calc(100% - 150px)"
      :body-style="{ height: '100%', overflow: 'auto' }"
      v-if="!templateDataLoading"
    >
      <Steps :current="current" :items="items" />
      <div v-show="current === 0" class="mt-12">
        <DynamicForm ref="form" :schema="schemaJSON" />
      </div>
      <div v-if="current === 1" class="mt-16">
        <PipelineConfig
          v-model:pipeline-data="pipelineData"
          @update:pipeline-data="pipelineData = $event"
        />
      </div>
    </Card>
    <div class="mt-4 flex justify-end">
      <Button v-if="current < steps.length - 1" type="primary" @click="next">
        下一步
      </Button>
      <Button v-if="current > 0" @click="prev" class="mr-4">上一步</Button>
      <Button
        v-if="current === steps.length - 1"
        @click="submit"
        type="primary"
      >
        创建任务
      </Button>
    </div>
  </Page>
</template>
