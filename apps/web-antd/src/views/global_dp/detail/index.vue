<script lang="ts" setup>
import type { GlobalDispatchDetail } from '#/api/global_dp/type';
import type { Shortcut, ShortcutCategory } from '#/api/region/type';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { EditOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Col,
  Collapse,
  CollapsePanel,
  Descriptions,
  DescriptionsItem,
  Divider,
  Row,
  Tag,
  Tooltip,
} from 'ant-design-vue';

import {
  getGlobalDispatchApi,
  getGlobalDispatchShortcutApi,
} from '#/api/global_dp/global_dp';
import GlobalDpStatus from '#/components/globaldpStatus/index.vue';
import RegionHistory from '#/components/regionHistory/index.vue';

import CanaryRestartDrawer from '../../region/detail/canaryRestartDrawer.vue';
import ExecDrawer from '../../region/detail/execDrawer.vue';
import UpdateModal from '../list/updateModal.vue';

const { setTabTitle } = useTabs();

const router = useRouter();
const dpName = router.currentRoute.value.params.name as string;
const spinning = ref<boolean>(false);
const globalDpDetail = ref<GlobalDispatchDetail>({} as GlobalDispatchDetail);
const activeKey = ref('1');

export interface InfoSummary {
  label: string;
  value: string;
  status: string;
}

const quickActions = ref<ShortcutCategory[]>();

const getQuickActionsData = async () => {
  quickActions.value = await getGlobalDispatchShortcutApi(dpName);
};

const [ExecDrawerDetail, execDrawerApi] = useVbenDrawer({
  connectedComponent: ExecDrawer,
  destroyOnClose: true,
});

const [CanaryRestartDrawerDetail, canaryRestartDrawerApi] = useVbenDrawer({
  connectedComponent: CanaryRestartDrawer,
  destroyOnClose: true,
});

const handleExec = (shortcut: Shortcut) => {
  if (shortcut.custom_component === 'canary_restart') {
    canaryRestartDrawerApi.setData({ region: globalDpDetail.value }).open();
    return;
  }
  execDrawerApi
    .setData({
      region: globalDpDetail.value,
      shortcut_data: shortcut,
    })
    .open();
};

const handleEdit = () => {
  updateFormModalApi
    .setData({
      callback: getGlobalDpDetail,
      ...globalDpDetail.value,
    })
    .open();
};

const [UpdateFormModal, updateFormModalApi] = useVbenModal({
  connectedComponent: UpdateModal,
});

const getGlobalDpDetail = async () => {
  globalDpDetail.value = await getGlobalDispatchApi(dpName);
};

onMounted(async () => {
  spinning.value = true;
  setTabTitle(`一级DP ${dpName}`);
  await getGlobalDpDetail();
  await getQuickActionsData();
  spinning.value = false;
});
</script>
<template>
  <Page auto-content-height v-spinning="spinning">
    <UpdateFormModal />
    <ExecDrawerDetail />
    <CanaryRestartDrawerDetail />
    <Card>
      <template #title>
        {{ `${globalDpDetail.name} 一级DP详情` }}
        <Button @click="handleEdit()" type="text">
          <EditOutlined />
          编辑
        </Button>
      </template>
      <Descriptions bordered :column="3" size="small" class="mb-6">
        <template #title> DP基础信息 </template>
        <DescriptionsItem label="区服名称">
          {{ globalDpDetail.name }}
        </DescriptionsItem>
        <DescriptionsItem label="区服列表名称">
          {{ globalDpDetail.region_list_name }}
        </DescriptionsItem>
        <DescriptionsItem label="数值分支">
          {{ globalDpDetail.data_branch }}
        </DescriptionsItem>
        <DescriptionsItem label="代码分支">
          {{ globalDpDetail.code_branch }}
        </DescriptionsItem>
        <DescriptionsItem label="运行分支">
          {{ globalDpDetail.global_dispatch_extra_info?.monitor_branch }}
        </DescriptionsItem>
        <DescriptionsItem
          label="进程信息"
          :span="2"
          v-if="globalDpDetail.global_dispatch_extra_info"
        >
          <template
            v-if="
              globalDpDetail.global_dispatch_extra_info
                .global_dispatch_process_info.is_healthy
            "
          >
            <Tag color="green">
              正常(
              {{
                `${globalDpDetail.global_dispatch_extra_info.global_dispatch_process_info.healthy_count}/${globalDpDetail.global_dispatch_extra_info.global_dispatch_process_info.healthy_count + globalDpDetail.global_dispatch_extra_info.global_dispatch_process_info.un_healthy_count}`
              }}
              )
            </Tag>
          </template>
          <template v-else>
            <Tag color="error">
              异常(
              {{
                `${globalDpDetail.global_dispatch_extra_info.global_dispatch_process_info.un_healthy_count}/${globalDpDetail.global_dispatch_extra_info.global_dispatch_process_info.healthy_count + globalDpDetail.global_dispatch_extra_info.global_dispatch_process_info.un_healthy_count}`
              }})
            </Tag>
          </template>
        </DescriptionsItem>
      </Descriptions>
      <Collapse
        v-model:active-key="activeKey"
        destroy-inactive-tab-pane
        class="mt-4"
      >
        <CollapsePanel key="1" :show-arrow="false">
          <template #header> 快捷操作 </template>
          <template v-for="(item, index) in quickActions" :key="index">
            <Divider
              orientation="left"
              :style="{
                fontSize: '14px',
                marginTop: index === 0 ? '0' : '12px',
                marginBottom: '12px',
              }"
            >
              {{ item.name }}
            </Divider>
            <Row :gutter="16">
              <Col :span="2" v-for="action in item.shortcuts" :key="action.uid">
                <template v-if="action.desc">
                  <Tooltip class="w-full" placement="top">
                    <template #title> {{ action.desc }} </template>
                    <Button class="w-full" @click="handleExec(action)">
                      {{ action.name }}
                    </Button>
                  </Tooltip>
                </template>
                <template v-else>
                  <Button class="w-full" @click="handleExec(action)">
                    {{ action.name }}
                  </Button>
                </template>
              </Col>
            </Row>
          </template>
        </CollapsePanel>
      </Collapse>
      <GlobalDpStatus
        v-if="globalDpDetail.name"
        :name="globalDpDetail.name"
        class="mt-4"
      />
      <RegionHistory
        v-if="globalDpDetail.name"
        :region-name="globalDpDetail.name"
      />
    </Card>
  </Page>
</template>
