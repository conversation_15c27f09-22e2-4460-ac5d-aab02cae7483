<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { queryDpHostData } from '#/api/global_dp/type';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getGlobalDispatchHostInfoApi } from '#/api/global_dp/global_dp';

const region = ref('');

const formOptions: VbenFormProps = reactive({
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'hostname',
      label: '主机名',
    },
    {
      component: 'Input',
      fieldName: 'ip',
      label: 'IP地址',
    },
    {
      component: 'Switch',
      fieldName: 'is_healthy',
      label: '健康状态',
      wrapperClass: 'w-[50px]',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
});

const gridOptions: VxeTableGridOptions<queryDpHostData> = {
  showOverflow: false,
  columns: [
    {
      title: '主机名',
      field: 'hostname',
      width: 250,
    },
    {
      title: 'IP地址',
      field: 'ip',
    },
    {
      title: '虚拟地址',
      field: 'vaddr',
    },
    {
      title: '健康状态',
      field: 'is_healthy',
      slots: { default: 'is_healthy' },
    },
    {
      title: '分支',
      field: 'branch',
      formatter: ({ cellValue }) => {
        return cellValue || '-';
      },
    },
    {
      title: '配置文件MD5',
      field: 'config_hash',
    },
    {
      title: 'commit_id',
      field: 'commit_id',
    },
    {
      title: 'build_time',
      field: 'build_time',
      width: 200,
    },
  ],
  exportConfig: {},
  height: '850px',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getGlobalDispatchHostInfoApi(region.value, {
          page: page.currentPage,
          size: page.pageSize,
          hostname: formValues.hostname,
          is_healthy: formValues.is_healthy,
          ip: formValues.ip,
        }).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
};

const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  showCancelButton: false,
  showConfirmButton: false,
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const data = modalApi.getData();
      region.value = data.region;
      if (formOptions.schema && formOptions.schema[3]) {
        formOptions.schema[3].defaultValue = data.is_healthy;
      }
    }
  },
  title: '查看区服机器信息',
  class: 'w-[1800px]',
});
</script>

<template>
  <Modal>
    <Grid>
      <template #is_healthy="{ row }">
        <Tag color="green" v-if="row.is_healthy"> 正常 </Tag>
        <Tag color="error" v-else> 异常 </Tag>
      </template>
    </Grid>
  </Modal>
</template>
