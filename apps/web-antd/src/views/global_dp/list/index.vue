<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';

import { Button, message, Popconfirm, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteGlobalDispatchApi,
  queryGlobalDispatchApi,
} from '#/api/global_dp/global_dp';

import CreateModal from './createModal.vue';
import ListHostInfoModal from './listHostInfoModal.vue';
import UpdateModal from './updateModal.vue';

const router = useRouter();

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '名称',
    },
    {
      component: 'Input',
      fieldName: 'description',
      label: '描述',
    },
  ],
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: false,
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [
    {
      title: '区服名称',
      field: 'name',
    },
    {
      title: '区服列表名称',
      field: 'region_list_name',
    },
    {
      title: '数值分支',
      field: 'data_branch',
    },
    {
      title: '代码分支',
      field: 'code_branch',
    },
    {
      title: '运行分支',
      field: 'global_dispatch_extra_info.monitor_branch',
    },
    {
      title: '进程状态',
      field: 'process_info',
      slots: { default: 'process_info' },
    },
    {
      title: '区服描述',
      field: 'description',
      formatter: ({ row }: { row: any }) => {
        return row.description || '-';
      },
    },
    {
      title: '创建人',
      field: 'created_by',
      visible: false,
    },
    {
      title: '更新人',
      field: 'updated_by',
    },
    {
      title: '创建时间',
      field: 'created_at',
      visible: false,
    },
    {
      title: '更新时间',
      field: 'updated_at',
    },
    {
      title: '操作',
      width: 260,
      field: 'actions',
      slots: { default: 'actions' },
    },
  ],
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await queryGlobalDispatchApi({
          name: formValues.name,
          description: formValues.description,
          page: page.currentPage,
          size: page.pageSize,
        }).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const handleCreate = () => {
  createFormModalApi
    .setData({
      callback: gridApi.reload,
    })
    .open();
};

const handleEdit = (row: any) => {
  updateFormModalApi
    .setData({
      callback: gridApi.reload,
      ...row,
    })
    .open();
};

const [CreateFormModal, createFormModalApi] = useVbenModal({
  connectedComponent: CreateModal,
});

const [UpdateFormModal, updateFormModalApi] = useVbenModal({
  connectedComponent: UpdateModal,
});

const [ListModal, listHostInfoModalApi] = useVbenModal({
  connectedComponent: ListHostInfoModal,
});

const handleView = (row: any) => {
  router.push({ name: 'global_dp_detail', params: { name: row.name } });
  // console.warn(row);
};

const handleDelete = (row: any) => {
  deleteGlobalDispatchApi(row.name).then(() => {
    message.success('删除成功');
    gridApi.reload();
  });
};

const showhostInfo = (region: string, is_healthy: boolean) => {
  listHostInfoModalApi.setData({ region, is_healthy }).open();
};
</script>

<template>
  <Page auto-content-height title="一级DP列表" description="展示所有的一级DP">
    <CreateFormModal />
    <UpdateFormModal />
    <ListModal />
    <Grid>
      <template #process_info="{ row }">
        <template
          v-if="
            row.global_dispatch_extra_info.global_dispatch_process_info
              .is_healthy
          "
        >
          <Tag
            color="green"
            @click="showhostInfo(row.name, true)"
            style="cursor: pointer"
          >
            正常(
            {{
              `${row.global_dispatch_extra_info.global_dispatch_process_info.healthy_count}/${row.global_dispatch_extra_info.global_dispatch_process_info.healthy_count + row.global_dispatch_extra_info.global_dispatch_process_info.un_healthy_count}`
            }}
            )
          </Tag>
        </template>
        <template v-else>
          <Tag
            color="error"
            @click="showhostInfo(row.name, false)"
            style="cursor: pointer"
          >
            异常(
            {{
              `${row.global_dispatch_extra_info.global_dispatch_process_info.un_healthy_count}/${row.global_dispatch_extra_info.global_dispatch_process_info.healthy_count + row.global_dispatch_extra_info.global_dispatch_process_info.un_healthy_count}`
            }})
          </Tag>
        </template>
      </template>
      <template #toolbar-tools>
        <Button type="primary" @click="handleCreate">创建</Button>
      </template>
      <template #actions="{ row }">
        <Button @click="handleView(row)" class="mr-2"> 查看 </Button>
        <Button @click="handleEdit(row)" class="mr-2" type="primary">
          编辑
        </Button>
        <Popconfirm
          ok-text="是"
          :title="`是否确认删除区服 ${row.name}`"
          cancel-text="否"
          @confirm="handleDelete(row)"
        >
          <Button type="default" danger> 删除 </Button>
        </Popconfirm>
      </template>
    </Grid>
  </Page>
</template>
