<script setup lang="ts">
import type { HostInfo } from '../hostSelection/type';

import { ref, watch } from 'vue';

import { Select } from 'ant-design-vue';

import {
  getJumpHosts,
  getRegionHostsApi,
  getRegionIdleHosts,
} from '#/api/region/region';

interface Props {
  region?: string;
  useIdleHosts?: boolean;
  useJumpHosts?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  region: '',
});

const value = defineModel<HostInfo | undefined>('value', { required: true });
const hosts = ref<HostInfo[]>([]);
const getRegionHosts = async (region: string) => {
  if (props.useJumpHosts) {
    hosts.value = [];
    await getJumpHosts(region)
      .then((res) => {
        hosts.value = res;
      })
      .finally(() => {});
  } else if (props.useIdleHosts) {
    hosts.value = [];
    // 区服已用机器
    await getRegionHostsApi(region)
      .then((res) => {
        hosts.value.push(...res.list);
      })
      .finally(() => {});
    // 同区服空闲机
    await getRegionIdleHosts(region)
      .then((res) => {
        hosts.value.push(
          ...res.list.filter((item: any) =>
            item.hostname.includes(region.replaceAll('_', '-')),
          ),
        );
      })
      .finally(() => {});
  } else {
    await getRegionHostsApi(region)
      .then((res) => {
        hosts.value = res.list;
      })
      .finally(() => {});
  }
};

const onChange = (event: any) => {
  value.value = hosts.value.find((item) => item.hostname === event) as HostInfo;
};

watch(
  () => props.region,
  async (val, old) => {
    if (!val || val === old) {
      // 当区域清空时，也清空选中的主机
      value.value = {
        hostname: '',
        ip: '',
        agent_id: '',
      };
      return;
    }
    await getRegionHosts(val).then(() => {
      // 设置默认值
      value.value = hosts.value[0];
    });
  },
  { immediate: true },
);
</script>

<template>
  <Select
    :value="value?.hostname"
    @change="onChange"
    :options="
      hosts.map((item) => ({ label: item.hostname, value: item.hostname }))
    "
  />
</template>
