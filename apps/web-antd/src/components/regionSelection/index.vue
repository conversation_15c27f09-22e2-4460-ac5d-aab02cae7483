<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Select } from 'ant-design-vue';

import { getRegionsNamesApi } from '#/api';

interface SelectOption {
  label: string;
  value: string;
  disabled?: boolean;
}

const props = withDefaults(
  defineProps<{
    disableLockedRegion?: boolean;
    excludeRegions?: string[];
    onlySfRegion?: boolean;
  }>(),
  {
    disableLockedRegion: true,
    excludeRegions: () => [],
    onlySfRegion: false,
  },
);

const value = defineModel<string | undefined>('value', { required: true });

const options = ref<SelectOption[]>([]);

onMounted(async () => {
  const res = await getRegionsNamesApi();
  let optionsValue = res.list.map((item: any) => ({
    label: item.name,
    value: item.name,
    disabled: props.disableLockedRegion && item.is_lock,
  }));
  if (props.excludeRegions.length > 0) {
    optionsValue = optionsValue.filter(
      (item) => !props.excludeRegions.includes(item.value),
    );
  } else if (props.onlySfRegion) {
    optionsValue = optionsValue.filter((item) => item.value.startsWith('sf_'));
  }
  options.value = optionsValue;
});
</script>

<template>
  <Select
    v-model:value="value"
    placeholder="请选择区服名称"
    :options="options"
    show-search
    allow-clear
  />
</template>
