<script lang="ts" setup>
import { useRouter } from 'vue-router';

import { Button, message, Popconfirm } from 'ant-design-vue';

import { actionPipelineApi } from '#/api/engine/engine';

const props = defineProps({
  action: {
    type: String,
    required: true,
  },
  pipelineId: {
    type: Number,
    required: true,
  },
  pipelineName: {
    type: String,
    required: true,
  },
  callback: {
    type: Function,
    required: false,
    default: () => {},
  },
  region: {
    type: String,
    required: false,
    default: '',
  },
});

const router = useRouter();

const actionLabelMap: { [key: string]: string } = {
  view: '查看',
  start: '开始',
  retry: '重试',
  stop: '停止',
  skip: '跳过',
  delete: '删除',
  confirm: '确认',
  continue: '手动继续',
};

const getActionLabel = (action: string) => {
  return actionLabelMap[action];
};

const handleAction = async (action: any, id: number) => {
  switch (action) {
    case 'confirm':
    case 'continue':
    case 'retry':
    case 'skip':
    case 'start':
    case 'stop': {
      await actionPipelineApi(id, action).then(() => {
        message.success('操作成功');
        if (props.callback) {
          props.callback();
        }
      });
      break;
    }
    case 'delete': {
      console.warn('删除');
      break;
    }
    case 'view': {
      if (props.region) {
        router.push({
          name: 'region_pipeline_detail',
          params: { id, name: props.region },
        });
        return;
      }
      router.push({ name: 'pipeline_detail', params: { id } });
      break;
    }
    default: {
      break;
    }
  }
};
</script>

<template>
  <template v-if="action === 'view'">
    <Button @click="handleAction('view', props.pipelineId)" class="mr-2">
      查看
    </Button>
  </template>
  <template
    v-else-if="
      action === 'start' ||
      action === 'delete' ||
      action === 'stop' ||
      action === 'retry' ||
      action === 'confirm' ||
      action === 'skip'
    "
  >
    <Popconfirm
      :title="`是否确认 ${getActionLabel(action)} 任务 ${props.pipelineName}`"
      ok-text="是"
      cancel-text="否"
      @confirm="handleAction(action, props.pipelineId)"
    >
      <Button
        class="mr-2"
        :type="
          action === 'start' || action === 'retry' || action === 'confirm'
            ? 'primary'
            : 'default'
        "
        :danger="action === 'stop' || action === 'delete'"
      >
        {{ getActionLabel(action) }}
      </Button>
    </Popconfirm>
  </template>
  <Button v-else @click="handleAction(action, props.pipelineId)" class="mr-2">
    {{ getActionLabel(action) }}
  </Button>
</template>
