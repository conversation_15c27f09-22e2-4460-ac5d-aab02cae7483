<script lang="ts" setup>
import { shallowRef } from 'vue';

import { VueMonacoEditor } from '@guolao/vue-monaco-editor';
import EditorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import CssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
import HtmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import JsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import TsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';

const props = withDefaults(
  defineProps<{
    language?: string;
    theme?: string;
  }>(),
  {
    language: 'json',
    theme: 'vs-dark',
  },
);

// @ts-expect-error
globalThis.MonacoEnvironment = {
  getWorker(_: any, label: string) {
    if (label === 'json') {
      return new JsonWorker();
    }
    if (label === 'css' || label === 'scss' || label === 'less') {
      return new CssWorker();
    }
    if (label === 'html' || label === 'handlebars' || label === 'razor') {
      return new HtmlWorker();
    }
    if (label === 'typescript' || label === 'javascript') {
      return new TsWorker();
    }
    return new EditorWorker();
  },
};

const MONACO_EDITOR_OPTIONS = {
  automaticLayout: true,
  formatOnType: true,
  formatOnPaste: true,
};

const code = defineModel<string>('value', { required: true });

const editor = shallowRef();
const handleMount = (editorInstance: any) => (editor.value = editorInstance);
</script>

<template>
  <VueMonacoEditor
    v-model:value="code"
    :options="MONACO_EDITOR_OPTIONS"
    :language="props.language"
    :theme="props.theme"
    @mount="handleMount"
  />
</template>
