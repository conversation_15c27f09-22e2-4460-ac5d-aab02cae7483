<script setup lang="ts">
import { ref } from 'vue';

import { useInfiniteQuery } from '@tanstack/vue-query';
import { Button, Timeline, TimelineItem, TypographyText } from 'ant-design-vue';

import { getPipelineLogsApi } from '#/api';

const props = defineProps({
  pipelineId: {
    type: Number,
    required: true,
  },
});

const pageSize = ref<number>(10);

const fetchProducts = async ({ pageParam = 1 }) => {
  return await getPipelineLogsApi(
    props.pipelineId,
    pageParam,
    pageSize.value,
  ).then((data) => {
    const res = {
      total: data.total,
      items: data.list,
    };
    return res;
  });
};

const timeLineColor = (status: string) => {
  switch (status) {
    case 'FAILED': {
      return 'red';
    }
    case 'RUNNING': {
      return 'blue';
    }
    case 'SUCCESS': {
      return 'green';
    }
    default: {
      return 'gray';
    }
  }
};

const {
  data,
  error,
  fetchNextPage,
  hasNextPage,
  isError,
  isFetching,
  isFetchingNextPage,
  isPending,
} = useInfiniteQuery({
  getNextPageParam: (current, allPages) => {
    const nextPage = allPages.length + 1;
    const allPagesCount = allPages
      .map((item) => item.items.length)
      .reduce((a, b) => a + b);
    if (allPagesCount === current.total) return;
    return nextPage;
  },
  initialPageParam: 0,
  queryFn: fetchProducts,
  queryKey: [],
});
</script>

<template>
  <div>
    <span v-if="isPending">加载...</span>
    <span v-else-if="isError">出错了: {{ error }}</span>
    <div v-else-if="data" class="mt-5">
      <span v-if="isFetching && !isFetchingNextPage">Fetching...</span>
      <Timeline>
        <template v-for="(group, index) in data.pages" :key="index">
          <TimelineItem
            v-for="(log, lIndex) in group.items"
            :key="`${index}-${lIndex}`"
            :color="timeLineColor(log.status)"
          >
            操作人: {{ log.created_by }} {{ log.log_detail }}
            <TypographyText type="secondary">
              {{ log.created_at }}
            </TypographyText>
          </TimelineItem>
        </template>
      </Timeline>
      <Button
        :disabled="!hasNextPage || isFetchingNextPage"
        @click="() => fetchNextPage()"
      >
        <span v-if="isFetchingNextPage">加载中...</span>
        <span v-else-if="hasNextPage">加载更多</span>
        <span v-else>没有更多了</span>
      </Button>
    </div>
  </div>
</template>
