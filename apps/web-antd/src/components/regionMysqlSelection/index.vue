<script setup lang="ts">
import type { SelectValue } from 'ant-design-vue/es/select';

import type { SelectOption } from '@vben/types';

import { computed, defineComponent, onMounted, ref } from 'vue';

import {
  Button,
  Divider,
  Dropdown,
  Input,
  InputPassword,
  Select,
  Table,
  Tag,
} from 'ant-design-vue';

import { getRegionMysql, getRegionMysqlIdx } from '#/api';

const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

interface MysqlInfo {
  instance_id: string;
  mysql_user: string;
  mysql_pwd: string;
  table_idx: string[];
}

const mysqlInfoColumns = [
  { title: '实例', dataIndex: 'instance_id', key: 'instance_id', width: 400 },
  { title: '用户名', dataIndex: 'user', key: 'user' },
  { title: '密码', dataIndex: 'pwd', key: 'pwd' },
  { title: '索引', dataIndex: 'idx', key: 'idx', width: 650 },
  { title: '操作', key: 'operation', width: 50 },
];

const mysqlLoading = ref<boolean>(false);
const mysqlIdxLoading = ref<boolean>(false);
const mysqlOptions = ref<SelectOption[]>([]);
const allMysqlIdx = ref<SelectOption[]>([]);

const getMysqlOptions = async () => {
  mysqlLoading.value = true;
  const data = await getRegionMysql();
  mysqlOptions.value = data.map((item: any) => ({
    value: item.db_cluster_id,
    label: item.db_cluster_description,
  }));
  mysqlLoading.value = false;
};

const getMysqlIdxOptions = async () => {
  mysqlIdxLoading.value = true;
  const data = await getRegionMysqlIdx();
  allMysqlIdx.value = data.map((item: any) => ({
    value: item.idx,
    label: item.name,
  }));
  mysqlIdxLoading.value = false;
};

onMounted(async () => {
  await Promise.all([getMysqlOptions(), getMysqlIdxOptions()]);
});

const mysqlInfos = defineModel<MysqlInfo[]>('value', { required: true });
const mysqlIdxOptions = computed(() => {
  const idxs = new Set(mysqlInfos.value.flatMap((item) => item.table_idx));
  return allMysqlIdx.value.filter((item) => !idxs.has(item.value));
});
const handleSelectAll = (
  selectedOptions: SelectValue[],
  allOptions: SelectOption[],
) => {
  const allValues = allOptions.map((item) => item.value);
  selectedOptions.push(...allValues);
};

const handleDeselectAll = (selectedOptions: SelectValue[]) => {
  selectedOptions.length = 0;
};

const deleteMysqlInfo = (record: any) => {
  const index = mysqlInfos.value.indexOf(record);
  if (index !== -1) {
    mysqlInfos.value.splice(index, 1);
  }
};

const addMysqlInfo = () => {
  mysqlInfos.value.push({
    instance_id: '',
    mysql_user: 'work',
    mysql_pwd: '',
    table_idx: [],
  });
};
</script>
<template>
  <div>
    <Table
      v-if="mysqlInfos.length > 0"
      :columns="mysqlInfoColumns"
      :data-source="mysqlInfos"
      size="small"
      :pagination="false"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'instance_id'">
          <Select
            v-model:value="record.instance_id"
            class="w-full"
            :options="mysqlOptions"
            :loading="mysqlLoading"
            option-filter-prop="label"
            show-search
          />
        </template>
        <template v-if="column.key === 'user'">
          <Input
            :value="record.mysql_user"
            placeholder="请输入用户"
            default-value="work"
          />
        </template>
        <template v-if="column.key === 'pwd'">
          <InputPassword
            v-model:value="record.mysql_pwd"
            placeholder="请输入密码"
            autocomplete="off"
          />
        </template>
        <template v-if="column.key === 'idx'">
          <Select
            v-model:value="record.table_idx"
            class="w-full"
            mode="multiple"
            :options="mysqlIdxOptions"
            :loading="mysqlIdxLoading"
            show-search
          >
            <!-- antd回显标签功能有问题，暂时用这种方法 -->
            <template #tagRender="{ value, closable, onClose }">
              <Tag
                :closable="closable"
                style="margin-right: 3px"
                @close="onClose"
              >
                {{ allMysqlIdx.find((item) => item.value === value)?.label }}
              </Tag>
            </template>
            <template #dropdownRender="{ menuNode: menu }">
              <VNodes :vnodes="menu" />
              <Divider style="margin: 4px 0" />
              <div class="dropdown-header">
                <Button
                  type="link"
                  size="small"
                  @click="handleSelectAll(record.table_idx, mysqlIdxOptions)"
                >
                  全选
                </Button>
                <Button
                  type="link"
                  size="small"
                  @click="handleDeselectAll(record.table_idx)"
                >
                  取消选择
                </Button>
              </div>
            </template>
          </Select>
        </template>
        <template v-if="column.key === 'operation'">
          <Button type="link" @click="deleteMysqlInfo(record)"> 删除 </Button>
        </template>
      </template>
    </Table>
    <Dropdown trigger="click">
      <Button class="w-full" type="dashed" @click="addMysqlInfo">
        添加实例
      </Button>
    </Dropdown>
  </div>
</template>
