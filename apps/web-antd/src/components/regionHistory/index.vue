<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { PipelineListData } from '#/api/engine/model';

import { Divider, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { queryPipelineApi } from '#/api/engine/engine';
import { ShortcutPipelineTemplateId } from '#/api/engine/model';
import { PipelineAction } from '#/components/pipelineAction';
import { StatusTag } from '#/components/statusTag';

const props = defineProps<{ regionName: string }>();

const gridOptions: VxeTableGridOptions<PipelineListData> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [
    {
      title: '流程名称',
      field: 'name',
      formatter({ row }) {
        return row.pipeline.name;
      },
    },
    {
      title: '区服',
      field: 'region',
      slots: { default: 'region' },
    },
    {
      title: '状态',
      field: 'status',
      slots: { default: 'status' },
    },
    {
      title: '当前步骤',
      field: 'current_step',
      width: 300,
      formatter({ row }) {
        const currentStage =
          row.pipeline.stages[row.pipeline.current_stage_idx];
        if (!currentStage) return '-';
        const stageName = currentStage.name;
        const taskName = currentStage.tasks[row.pipeline.current_task_idx]
          ? currentStage.tasks[row.pipeline.current_task_idx]?.name
          : '-';
        return `步骤${row.pipeline.current_stage_idx + 1}【${stageName}】 - 任务${row.pipeline.current_task_idx + 1}【${taskName}】`;
      },
    },
    {
      title: '影响机器数',
      field: 'host_count',
      titleHelp: {
        content: '仅展示快捷操作的影响机器数',
      },
      formatter({ row }) {
        if (row.pipeline.pipeline_template_id === ShortcutPipelineTemplateId) {
          let total = 0;
          for (const stage of row.pipeline.stages) {
            for (const task of stage.tasks) {
              for (const subtask of task.subtasks) {
                const instanceValue = subtask.plugin.input?.find(
                  (item) => item.key === 'instance_params',
                )?.value;
                if (!instanceValue) {
                  continue;
                }
                total += instanceValue.length;
              }
            }
          }
          return total;
        }
        return '-';
      },
    },
    {
      title: '创建人',
      field: 'creator',
      formatter({ row }) {
        return row.pipeline.created_by;
      },
    },
    {
      title: '开始时间',
      field: 'start_time',
      formatter({ row }) {
        return row.pipeline.created_at;
      },
    },
    {
      title: '结束时间',
      field: 'end_time',
      formatter({ row }) {
        if (
          row.pipeline.status === 'SUCCESS' ||
          row.pipeline.status === 'STOPPED'
        ) {
          return row.pipeline.updated_at;
        }
        return '-';
      },
    },
    {
      title: '备注',
      field: 'description',
      formatter({ row }) {
        return row.pipeline.description;
      },
    },
    { title: '操作', field: 'actions', slots: { default: 'actions' } },
  ],
  exportConfig: {},
  keepSource: true,
  pagerConfig: {
    pageSize: 10,
  },
  minHeight: 260,
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        return await queryPipelineApi(
          '',
          props.regionName,
          page.currentPage,
          page.pageSize,
        ).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          return res;
        });
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
</script>

<template>
  <div class="mt-4">
    <Divider>历史执行记录</Divider>
    <Grid>
      <template #status="{ row }">
        <StatusTag :status="row.pipeline.status" />
      </template>
      <template #region="{ row }">
        <Tag>
          {{
            row.pipeline?.variables?.find((item: any) => item.key === 'region')
              ?.value
          }}
        </Tag>
      </template>
      <template #actions="{ row }">
        <PipelineAction
          action="view"
          :pipeline-id="row.pipeline.id"
          :pipeline-name="row.pipeline.name"
          :region="props.regionName"
        />
        <template v-for="action in row.actions" :key="action">
          <PipelineAction
            :action="action"
            :pipeline-id="row.pipeline.id"
            :pipeline-name="row.pipeline.name"
            :callback="gridApi.query"
            :region="props.regionName"
          />
        </template>
      </template>
    </Grid>
  </div>
</template>
