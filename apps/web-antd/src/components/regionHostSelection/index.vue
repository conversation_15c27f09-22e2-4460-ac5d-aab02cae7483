<script setup lang="ts">
import type { HostInfo } from '../hostSelection/type';

import { ref, watch } from 'vue';

import {
  getJumpHosts,
  getRegionHostsApi,
  getRegionIdleHosts,
} from '#/api/region/region';

import { HostSelection } from '../hostSelection';

interface Props {
  region?: string;
  useIdleHosts?: boolean;
  useJumpHosts?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  region: '',
  useIdleHosts: false,
  useJumpHosts: false,
});

const value = defineModel<HostInfo[]>('value', { required: true });
const hosts = ref<HostInfo[]>([]);
const getRegionHosts = async (region: string) => {
  if (props.useJumpHosts) {
    hosts.value = [];
    await getJumpHosts(region)
      .then((res) => {
        hosts.value = res;
      })
      .finally(() => {});
  } else if (props.useIdleHosts) {
    hosts.value = [];
    // 区服已用机器
    await getRegionHostsApi(region)
      .then((res) => {
        hosts.value.push(...res.list);
      })
      .finally(() => {});
    // 同区服空闲机
    await getRegionIdleHosts(region)
      .then((res) => {
        hosts.value.push(
          ...res.list.filter((item: any) =>
            item.hostname.includes(region.replaceAll('_', '-')),
          ),
        );
      })
      .finally(() => {});
  } else {
    await getRegionHostsApi(region)
      .then((res) => {
        hosts.value = res.list;
      })
      .finally(() => {});
  }
};

watch(
  () => props.region,
  async (val, old) => {
    if (!val || val === old) {
      // 当区域清空时，也清空选中的主机
      value.value = [];
      return;
    }
    await getRegionHosts(val);
  },
  { immediate: true },
);
</script>

<template>
  <HostSelection :hosts="hosts" v-model:value="value" />
</template>
