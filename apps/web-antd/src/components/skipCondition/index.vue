<script lang="ts" setup>
import { Col, Row, Select, Switch } from 'ant-design-vue';

const props = defineProps<{
  disabled?: boolean;
}>();

interface skipConditon {
  is_enabled: boolean;
  is_matched: boolean;
  name: string;
  code: string;
}

const condition = defineModel<skipConditon>('value', { required: true });

const conditionOptions = [
  {
    label: '区服停服',
    value: 'region.stop',
  },
  {
    label: '区服为官服',
    value: 'region.is_gf',
  },
];
</script>

<template>
  <div>
    <Row type="flex" align="middle" :gutter="20" class="mt-1">
      <Col :span="3">
        <Switch
          v-model:checked="condition.is_enabled"
          un-checked-children="未开启"
          checked-children="已开启"
          :disabled="props.disabled"
        />
      </Col>
    </Row>
    <Row type="flex" align="middle" :gutter="20" class="mt-2">
      <template v-if="condition.is_enabled">
        当
        <Col :span="3">
          <Switch
            v-model:checked="condition.is_matched"
            un-checked-children="不满足"
            checked-children="满足"
            :disabled="props.disabled"
          />
        </Col>
        <Col :span="15">
          <Select
            placeholder="请选择条件"
            v-model:value="condition.code"
            :options="conditionOptions"
            :disabled="props.disabled"
            allow-clear
          />
        </Col>
        时跳过此任务
      </template>
    </Row>
  </div>
</template>
