<script lang="ts" setup>
import type { HostInfo } from '../hostSelection/type';

import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Button } from 'ant-design-vue';

import HostSelection from '../hostSelection/index.vue';

const props = defineProps<{
  hosts: HostInfo[];
}>();

const value = defineModel<HostInfo[][]>('value', { required: true });

const getLeftHosts = (index: number) => {
  const otherSelectedHosts = new Set(
    value.value.flatMap((item, idx) => {
      if (idx === index) {
        return [];
      }
      return item.map((host) => host.hostname);
    }),
  );
  return props.hosts.filter((host) => !otherSelectedHosts.has(host.hostname));
};

// const leftHosts = computed(() => {
//   const selectedHosts = new Set(value.value.flat());
//   console.log('selectedHosts:', selectedHosts);
//   console.log(
//     'leftHosts:',
//     props.hosts.filter((host) => !selectedHosts.has(host)),
//   );
//   return props.hosts.filter((host) => !selectedHosts.has(host));
// });

const handleAdd = () => {
  value.value = [...value.value, []];
};

const handleDelete = (index: number) => {
  value.value.splice(index, 1);
};
</script>
<template>
  <div>
    <div
      v-for="(_, index) in value"
      :key="index"
      style="display: flex; align-items: center"
      class="mb-1"
    >
      <span class="mr-2" style="font-weight: bold">批次 {{ index + 1 }}:</span>
      <HostSelection
        v-model:value="value[index]!"
        :hosts="getLeftHosts(index)"
      />
      <Button
        type="dashed"
        @click="handleDelete(index)"
        style="margin-left: auto"
      >
        <MinusOutlined />
      </Button>
    </div>
    <div>
      <Button class="w-full" type="dashed" @click="handleAdd()">
        <PlusOutlined />
        新增批次
      </Button>
    </div>
  </div>
</template>
