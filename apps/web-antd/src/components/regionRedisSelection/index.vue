<script setup lang="ts">
import type { SelectValue } from 'ant-design-vue/es/select';

import type { SelectOption } from '@vben/types';

import { computed, defineComponent, onMounted, ref } from 'vue';

import {
  Button,
  Divider,
  Dropdown,
  InputPassword,
  Select,
  Table,
  Tag,
} from 'ant-design-vue';

import { getRegionRedis, getRegionRedisIdx } from '#/api/region/region';

const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

interface RedisInfo {
  instance_id: string;
  redis_pwd: string;
  idx: string[];
}

const redisInfos = defineModel<RedisInfo[]>('value', { required: true });
const redisOptions = ref<SelectOption[]>([]);
const allRedisIdx = ref<SelectOption[]>([]);
const redisLoading = ref<boolean>(false);
const redisIdxLoading = ref<boolean>(false);
const redisInfoColumns = [
  { title: '实例', dataIndex: 'instance_id', key: 'instance_id', width: 400 },
  { title: '密码', dataIndex: 'pwd', key: 'pwd' },
  { title: '索引', dataIndex: 'idx', key: 'idx', width: 650 },
  { title: '操作', key: 'operation', width: 50 },
];

const redisIdxOption = computed(() => {
  const idxs = new Set(redisInfos.value.flatMap((item) => item.idx));
  return allRedisIdx.value.filter((item) => !idxs.has(item.value));
});

const deleteRedisInfo = (record: any) => {
  const index = redisInfos.value.indexOf(record);
  if (index !== -1) {
    redisInfos.value.splice(index, 1);
  }
};

const addRedisInfo = () => {
  redisInfos.value.push({
    instance_id: '',
    redis_pwd: '',
    idx: [],
  });
};

const getRedisOptions = async () => {
  redisLoading.value = true;
  const data = await getRegionRedis();
  redisOptions.value = data.map((item: any) => ({
    value: item.instance_id,
    label: item.instance_name,
  }));
  redisLoading.value = false;
};

const getRedisIdxOptions = async () => {
  redisIdxLoading.value = true;
  const data = await getRegionRedisIdx();
  allRedisIdx.value = data.map((item: any) => ({
    value: item.idx,
    label: item.name,
  }));
  redisIdxLoading.value = false;
};

const handleSelectAll = (
  selectedOptions: SelectValue[],
  allOptions: SelectOption[],
) => {
  const allValues = allOptions.map((item) => item.value);
  selectedOptions.push(...allValues);
};

const handleDeselectAll = (selectedOptions: SelectValue[]) => {
  selectedOptions.length = 0;
};

onMounted(async () => {
  await Promise.all([getRedisOptions(), getRedisIdxOptions()]);
});
</script>
<template>
  <div>
    <Table
      v-if="redisInfos.length > 0"
      :columns="redisInfoColumns"
      :data-source="redisInfos"
      size="small"
      :pagination="false"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'instance_id'">
          <Select
            v-model:value="record.instance_id"
            class="w-full"
            :options="redisOptions"
            :loading="redisLoading"
            option-filter-prop="label"
            show-search
          />
        </template>
        <template v-if="column.key === 'pwd'">
          <InputPassword
            v-model:value="record.redis_pwd"
            placeholder="请输入密码"
            autocomplete="off"
          />
        </template>
        <template v-if="column.key === 'idx'">
          <Select
            v-model:value="record.idx"
            class="w-full"
            mode="multiple"
            :options="redisIdxOption"
            :loading="redisIdxLoading"
            option-filter-prop="label"
            show-search
          >
            <!-- antd回显标签功能有问题，暂时用这种方法 -->
            <template #tagRender="{ value, closable, onClose }">
              <Tag
                :closable="closable"
                style="margin-right: 3px"
                @close="onClose"
              >
                {{ allRedisIdx.find((item) => item.value === value)?.label }}
              </Tag>
            </template>
            <template #dropdownRender="{ menuNode: menu }">
              <VNodes :vnodes="menu" />
              <Divider style="margin: 4px 0" />
              <div class="dropdown-header">
                <Button
                  type="link"
                  size="small"
                  @click="handleSelectAll(record.idx, redisIdxOption)"
                >
                  全选
                </Button>
                <Button
                  type="link"
                  size="small"
                  @click="handleDeselectAll(record.idx)"
                >
                  取消选择
                </Button>
              </div>
            </template>
          </Select>
        </template>
        <template v-if="column.key === 'operation'">
          <Button type="link" @click="deleteRedisInfo(record)"> 删除 </Button>
        </template>
      </template>
    </Table>
    <Dropdown trigger="click">
      <Button class="w-full" type="dashed" @click="addRedisInfo">
        添加实例
      </Button>
    </Dropdown>
  </div>
</template>
