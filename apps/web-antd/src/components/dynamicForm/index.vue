<script setup lang="ts">
import { useVbenForm } from '#/adapter/form';

import { JSONToSchema } from './util';

interface Props {
  schema: string;
}

const props = defineProps<Props>();
const realSchema = JSONToSchema(props.schema);

const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: realSchema,
  showDefaultActions: false,
});

defineExpose({
  validate: async () => {
    return await formApi.validate();
  },
  region: async () => {
    return await formApi.getValues().then((res) => {
      return res.region;
    });
  },
  values: async () => {
    return await formApi.getValues().then((res) => {
      return Object.keys(res).map((key: string) => {
        const fieldOption = realSchema.find(
          (item: any) => item.fieldName === key,
        );
        return {
          name: fieldOption?.label,
          key,
          type: 'plain',
          value: res[key],
        };
      });
    });
  },
});
</script>

<template>
  <Form />
</template>
