import type { VbenFormSchema } from '#/adapter/form';

// schema中会使用h函数，需要导入
import { h } from 'vue';

// schema中会使用requestClient，需要导入
import { requestClient } from '#/api/request';

const executeDynamicCode = (codeToExecute: string) => {
  // 定义可以传递给动态代码的模块和变量
  const dependencies = {
    h,
    requestClient,
  };
  const dependencyKeys = Object.keys(dependencies);
  const dependencyValues = Object.values(dependencies);

  // eslint-disable-next-line no-new-func
  const dynamicFuncWrapper = new Function(
    ...dependencyKeys,
    `return (${codeToExecute})`,
  );
  const evaluatedResult = dynamicFuncWrapper(...dependencyValues);
  return evaluatedResult;
};

/**
 * 将含有函数的 Schema 对象转换为 JSON 字符串。
 * 它通过一个 replacer 函数来处理函数类型。
 *
 * @param schema - 符合 FormSchema 类型的 schema 数组。
 * @param space - JSON.stringify 的第三个参数，用于格式化输出，默认为 2。
 * @returns {string} - 转换后的 JSON 字符串。
 */
export function SchemaToJSON(
  schema: VbenFormSchema[],
  space: number = 2,
): string {
  const replacer = (_: string, value: any): any => {
    if (typeof value === 'function') {
      return value.toString(); // 将函数转换为其源码字符串
    }
    return value;
  };

  return JSON.stringify(schema, replacer, space);
}

/**
 * 将包含函数字符串的 JSON 还原为可执行的 FormSchema 对象。
 * 警告：此函数在内部使用了 new Function()，请务必确保 JSON 来源是完全可信的，以防止代码注入等安全风险。
 *
 * @param jsonString - 由 schemaToJSON 生成的 JSON 字符串。
 * @returns {VbenFormSchema[]} - 还原后的 schema 对象，如果解析失败则返回[]。
 */
export function JSONToSchema(jsonString: string): VbenFormSchema[] {
  try {
    // 首先解析为未知结构，因为函数还只是字符串
    const schema = JSON.parse(jsonString) as any[];

    // 递归遍历对象，将函数字符串转换回真正的函数
    const reviveFunctions = (obj: any): void => {
      for (const key in obj) {
        // eslint-disable-next-line no-prototype-builtins
        if (!obj.hasOwnProperty(key)) continue;
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          reviveFunctions(obj[key]);
        } else if (typeof obj[key] === 'string') {
          // 简单的启发式检查，判断字符串是否可能是一个函数
          const strValue = obj[key].trim();
          const isFunctionString =
            (strValue.startsWith('function') ||
              strValue.startsWith('(') ||
              strValue.includes('=>')) &&
            strValue.endsWith('}');

          if (isFunctionString) {
            try {
              obj[key] = executeDynamicCode(`(${strValue})`);
            } catch (error) {
              console.error(
                `Error evaluating function string for key "${key}":`,
                error,
              );
            }
          }
        }
      }
    };

    reviveFunctions(schema);

    return schema;
  } catch (error) {
    console.error('Failed to parse JSON string:', error);
    return [];
  }
}
