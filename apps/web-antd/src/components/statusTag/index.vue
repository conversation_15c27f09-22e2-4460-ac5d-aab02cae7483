<script lang="ts" setup>
import { computed } from 'vue';

import { SyncOutlined } from '@ant-design/icons-vue';
import { Tag } from 'ant-design-vue';

const props = defineProps({
  status: {
    type: String,
    required: true,
  },
});

const statusMap: { [key: string]: string } = {
  READY: '准备中',
  RUNNING: '运行中',
  SUCCESS: '已完成',
  FAILED: '失败',
  STOPPED: '已停止',
  SKIPPED: '已跳过',
  UNCONFIRMED: '待确认',
  WAITING: '等待中',
};
const statusText = computed(() => {
  return statusMap[props.status] ?? '未知';
});
</script>
<template>
  <Tag v-if="props.status === 'SUCCESS'" color="green">{{ statusText }}</Tag>
  <Tag v-else-if="status === 'FAILED'" color="red">{{ statusText }}</Tag>
  <Tag v-else-if="status === 'READY'" color="cyan">{{ statusText }}</Tag>
  <Tag v-else-if="status === 'STOPPED'" color="red">{{ statusText }}</Tag>
  <Tag v-else-if="status === 'RUNNING'" color="processing">
    <template #icon>
      <SyncOutlined :spin="true" />
    </template>
    {{ statusText }}
  </Tag>
  <Tag v-else color="gray">{{ statusText }}</Tag>
</template>
