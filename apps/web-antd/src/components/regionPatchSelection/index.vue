<script lang="ts" setup>
import type { RegionPatchInfo } from '#/api/region/type';

import { onMounted, ref } from 'vue';

import { Select, TypographyText } from 'ant-design-vue';

import { getRegionPatchApi } from '#/api';

const props = defineProps<{
  region: string;
}>();

const value = defineModel<string | undefined>('value', { required: true });

const regionPatchLoading = ref<boolean>(false);
const regionModulePatchInfoOptions = ref<Map<string, RegionPatchInfo[]>>(
  new Map(),
);

const getRegionPatchInfoOption = async (region: string) => {
  regionPatchLoading.value = true;
  await getRegionPatchApi(region)
    .then((data: any) => {
      regionPatchLoading.value = false;
      regionModulePatchInfoOptions.value = new Map<string, RegionPatchInfo[]>();
      for (const cur of data) {
        const moduleName = cur.module;
        const patchOptions = cur.patch_infos.map((item: any) => ({
          update_time: item.update_time,
          patch_id: item.patch_id,
        }));
        regionModulePatchInfoOptions.value.set(moduleName, patchOptions);
      }
    })
    .catch((_) => (regionPatchLoading.value = false));
};

onMounted(async () => {
  await getRegionPatchInfoOption(props.region);
});
</script>
<template>
  <Select
    v-model:value="value"
    allow-clear
    show-search
    :loading="regionPatchLoading"
  >
    <template
      v-for="[moduleName, patchOptions] in regionModulePatchInfoOptions"
      :key="moduleName"
    >
      <Select.OptGroup :label="moduleName">
        <Select.Option
          v-for="option in patchOptions"
          :key="option.patch_id"
          :value="option.patch_id"
        >
          {{ option.patch_id }}
          <TypographyText type="secondary" class="ml-2">
            {{ option.update_time }}
          </TypographyText>
        </Select.Option>
      </Select.OptGroup>
    </template>
  </Select>
</template>
