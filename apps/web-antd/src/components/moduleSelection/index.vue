<script lang="ts" setup>
import type { MenuProps, TableColumnType } from 'ant-design-vue';

import type { RegionPatchInfo } from '#/api/region/type';
import type { HostInfo } from '#/components/hostSelection/type';

import { computed, ref, watch } from 'vue';

import { DownOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Dropdown,
  Input,
  InputNumber,
  Menu,
  MenuItem,
  RadioButton,
  RadioGroup,
  Select,
  Table,
} from 'ant-design-vue';

import {
  getRegionHostsApi,
  getRegionIdleHosts,
  getRegionPatchApi,
} from '#/api/region/region';
import { HostSelection } from '#/components/hostSelection';
// 定义模块类型
export interface ModuleHost {
  module: string;
  hosts: HostInfo[];
  process_num?: number;
  patch?: string;
}

interface defaultProcessNum {
  [key: string]: number;
}

// 定义组件属性
interface Props {
  region?: string;
  showPatchId?: boolean; // 是否显示patch列
  showProcessNum?: boolean; // 是否显示进程数列
  useIdleHosts?: boolean; // 是否使用空闲机器
  enableModuleModification?: boolean; // 是否允许修改模块
  moduleDefaultProcessNum?: defaultProcessNum; // 模块默认进程数
  showModeSelection?: boolean; // 是否显示部署模式选择
  showCanaryQuickSelect?: boolean; // 是否显示灰度快速选择
}

const props = withDefaults(defineProps<Props>(), {
  showPatchId: true,
  showProcessNum: true,
  enableModuleModification: true,
  useIdleHosts: false,
  region: '',
  showModeSelection: true,
  moduleDefaultProcessNum: () => ({}),
  showCanaryQuickSelect: false,
});

// 使用defineModel来实现双向绑定
const moduleHosts = defineModel<ModuleHost[]>('value', { required: true });
const hosts = ref<HostInfo[]>([]);

const mode = ref<string>('multi');

// 模块选项
const moduleOptions = [
  { value: 'dp', label: 'dispatch' },
  { value: 'muip', label: 'muipserver' },
  { value: 'oa', label: 'oaserver' },
  { value: 'ns', label: 'nodeserver' },
  { value: 'gs', label: 'gameserver' },
  { value: 'rank', label: 'rankserver' },
  { value: 'gate', label: 'gateserver' },
  { value: 'mail', label: 'mailserver' },
  { value: 'sns', label: 'snsserver' },
  { value: 'fightmgr', label: 'fightmgrserver' },
  { value: 'match', label: 'matchserver' },
  { value: 'dg', label: 'dbgate' },
  { value: 'ufight', label: 'ufightserver' },
  { value: 'fight', label: 'fightserver' },
  { value: 'room', label: 'roomserver' },
  { value: 'glbdp', label: 'globaldispatch' },
];

// 计算未选择的模块选项
const computedOptions = computed(() => {
  return moduleOptions
    .filter((item) => {
      if (
        !props.region.includes('_globaldispatch_') &&
        item.label === 'globaldispatch'
      ) {
        return false;
      }
      if (moduleHosts.value.length === 0) {
        return true;
      }
      return !moduleHosts.value.some((module) => module.module === item.label);
    })
    .filter((item) => item.label.includes(filterModule.value.trim()));
});

// 动态生成表格列配置
const moduleColumns = computed<TableColumnType[]>(() => {
  const columns: TableColumnType[] = [
    { title: '模块名称', dataIndex: 'module', key: 'module' },
    { title: '模块机器', dataIndex: 'hosts', key: 'hosts' },
  ];

  // 根据showPatchId属性决定是否显示patch列
  if (props.showPatchId) {
    columns.push({
      title: 'patch',
      dataIndex: 'patch',
      key: 'patch',
      width: 600,
    });
  }

  // 根据showProcessNum属性决定是否显示进程数列
  if (props.showProcessNum) {
    columns.push({
      title: '进程数',
      dataIndex: 'process_num',
      key: 'process_num',
      width: 100,
    });
  }

  if (props.enableModuleModification) {
    columns.push({
      title: '操作',
      key: 'operation',
      width: 100,
    });
  }
  return columns;
});

// 删除模块
const deleteModule = (record: ModuleHost) => {
  const index = moduleHosts.value.indexOf(record);
  if (index !== -1) {
    moduleHosts.value.splice(index, 1);
  }
};

const filterModule = ref<string>('');

// 添加模块
const addModule: MenuProps['onClick'] = (e) => {
  const newValue: ModuleHost = {
    module: String(e.key),
    hosts: [],
  };
  if (props.showPatchId) {
    newValue.patch = '';
  }
  if (props.showProcessNum) {
    newValue.process_num = props.moduleDefaultProcessNum[newValue.module] || 1; // 默认进程数为1
  }
  moduleHosts.value.push(newValue);
};

const regionPatchLoading = ref<boolean>(false);
const regionModulePatchInfoOptions = ref<Map<string, RegionPatchInfo[]>>(
  new Map(),
);
const getRegionPatchInfoOption = async (region: string) => {
  regionPatchLoading.value = true;
  await getRegionPatchApi(region).then((data: any) => {
    regionPatchLoading.value = false;
    regionModulePatchInfoOptions.value = new Map<string, RegionPatchInfo[]>();
    for (const cur of data) {
      const moduleName = cur.module;
      const patchOptions = cur.patch_infos.map((item: any) => ({
        update_time: item.update_time,
        patch_id: item.patch_id,
      }));
      regionModulePatchInfoOptions.value.set(moduleName, patchOptions);
    }
  });
};

const getDefaultModules = (module: string) => {
  const findValue = moduleOptions.find((item) => item.label === module)?.value;
  return findValue ? [findValue] : [];
};

const getRegionHosts = async (region: string) => {
  if (props.useIdleHosts) {
    hosts.value = [];
    // 区服已用机器
    await getRegionHostsApi(region)
      .then((res) => {
        hosts.value.push(...res.list);
      })
      .finally(() => {});
    // 同区服空闲机
    await getRegionIdleHosts(region)
      .then((res) => {
        hosts.value.push(
          ...res.list.filter((item: any) =>
            item.hostname.includes(region.replaceAll('_', '-')),
          ),
        );
      })
      .finally(() => {});
    return;
  }

  await getRegionHostsApi(region)
    .then((res) => {
      hosts.value = res.list;
    })
    .finally(() => {});
};

const singleHosts = ref<HostInfo[]>([]);

const handleQuickSelect = (modules: string[]) => {
  const newModuleHosts = modules.map((module) => ({
    module,
    hosts: [],
  }));
  moduleHosts.value = newModuleHosts;
};

watch(
  () => props.region,
  async (val, old) => {
    if (!val || val === old) {
      return;
    }
    await Promise.all([
      getRegionHosts(val),
      props.showPatchId && getRegionPatchInfoOption(val),
    ]);
  },
  { immediate: true },
);

watch(
  singleHosts,
  () => {
    if (mode.value === 'single') {
      moduleHosts.value = moduleOptions
        .filter((item) => item.label !== 'glbdp') // 单机部署没有globaldispatch模块
        .map((item) => ({
          module: item.label,
          hosts: singleHosts.value,
        }));
    }
  },
  { deep: true },
);
</script>

<template>
  <div>
    <template v-if="props.showModeSelection">
      部署模式:
      <RadioGroup v-model:value="mode" class="mb-4 ml-2">
        <RadioButton value="single">单机部署</RadioButton>
        <RadioButton value="multi">多模块部署</RadioButton>
      </RadioGroup>
    </template>
    <template v-if="mode === 'single'">
      <HostSelection
        v-model:value="singleHosts"
        :hosts="hosts"
        text="选择单机部署机器"
      />
    </template>
    <template v-if="mode === 'multi'">
      <Table
        v-if="moduleHosts.length > 0"
        :columns="moduleColumns"
        :data-source="moduleHosts"
        size="small"
        :pagination="moduleHosts.length > 10 ? { pageSize: 10 } : false"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'module'">
            {{ record.module }}
          </template>
          <template v-if="column.key === 'hosts'">
            <HostSelection
              :hosts="hosts"
              :default-modules="getDefaultModules(record.module)"
              v-model:value="record.hosts"
            />
          </template>
          <template v-if="column.key === 'patch' && showPatchId">
            <Select
              v-model:value="record.patch"
              class="w-full"
              allow-clear
              :loading="regionPatchLoading"
            >
              <Select.Option
                v-for="option in regionModulePatchInfoOptions.get(
                  record.module,
                )"
                :key="option.patch_id"
                :value="option.patch_id"
              >
                {{ option.patch_id }}({{ option.update_time }})
              </Select.Option>
            </Select>
          </template>
          <template v-if="column.key === 'process_num'">
            <InputNumber v-model:value="record.process_num" />
          </template>
          <template v-if="column.key === 'operation'">
            <Button type="link" @click="deleteModule(record as ModuleHost)">
              删除
            </Button>
          </template>
        </template>
      </Table>
      <Dropdown trigger="click" v-if="enableModuleModification">
        <template #overlay>
          <Menu @click="addModule">
            <Input
              placeholder="请输入模块名称"
              style="width: 100%"
              v-model:value="filterModule"
            />
            <MenuItem v-for="option in computedOptions" :key="option.label">
              {{ option.label }}
            </MenuItem>
            <Button
              type="link"
              v-if="showCanaryQuickSelect"
              @click="handleQuickSelect(['dispatch'])"
            >
              灰度DP
            </Button>
            <Button
              type="link"
              v-if="showCanaryQuickSelect"
              @click="handleQuickSelect(['oaserver', 'muipserver'])"
            >
              灰度OA/MUIP
            </Button>
          </Menu>
        </template>
        <Button class="w-full" type="dashed" v-if="computedOptions.length > 0">
          添加模块
          <DownOutlined />
        </Button>
      </Dropdown>
    </template>
  </div>
</template>
