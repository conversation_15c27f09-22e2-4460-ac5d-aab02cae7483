<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Descriptions, DescriptionsItem, Divider } from 'ant-design-vue';
import dayjs from 'dayjs';

import { StatusTag } from '#/components/statusTag';

const [Drawer, drawerApi] = useVbenDrawer({
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});
const getTriggetType = (type: string) => {
  switch (type) {
    case 'auto': {
      return '自动触发';
    }
    case 'manual': {
      return '手动触发';
    }
    case 'timer': {
      return '定时触发';
    }
    default: {
      return '未知触发类型';
    }
  }
};

const getTimerTriggetType = (trigger_type: string) => {
  switch (trigger_type) {
    case 'fixed_delay': {
      return '延迟触发';
    }
    case 'fixed_time': {
      return '指定时间触发';
    }
    default: {
      return '未知触发类型';
    }
  }
};

const data = ref();
</script>
<template>
  <Drawer append-to-main title="步骤详情" class="w-[900px]">
    <Divider>基础信息</Divider>
    <Descriptions bordered :column="2">
      <DescriptionsItem label="步骤序号">{{ data.index }}</DescriptionsItem>
      <DescriptionsItem label="步骤名称">{{ data.name }}</DescriptionsItem>
      <DescriptionsItem label="步骤状态">
        <StatusTag :status="data.status" />
      </DescriptionsItem>
      <DescriptionsItem label="触发方式">
        {{ getTriggetType(data.trigger) }}
      </DescriptionsItem>
      <DescriptionsItem label="定时触发配置" v-if="data.trigger === 'timer'">
        <div>
          触发类型:
          {{ getTimerTriggetType(data.timer_trigger_config.trigger_type) }}
        </div>
        <div v-if="data.timer_trigger_config.trigger_type === 'fixed_time'">
          固定时间: {{ data.timer_trigger_config.fixed_time }}
        </div>
        <div v-if="data.timer_trigger_config.trigger_type === 'fixed_delay'">
          延迟时间: {{ data.timer_trigger_config.fixed_delay }}
          {{ data.timer_trigger_config.fixed_delay_unit }}
        </div>
        <div>
          预计触发时间:
          {{
            dayjs(
              data.timer_trigger_config.expected_trigger_time * 1000,
            ).format('YYYY-MM-DD HH:mm:ss')
          }}
        </div>
        <div>
          实际触发时间:
          {{
            data.timer_trigger_config.real_trigger_time
              ? dayjs(
                  data.timer_trigger_config.real_trigger_time * 1000,
                ).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </div>
      </DescriptionsItem>
    </Descriptions>
  </Drawer>
</template>
