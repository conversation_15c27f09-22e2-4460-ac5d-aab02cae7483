<script setup lang="ts">
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Button } from 'ant-design-vue';

import { Pipeline } from '#/components/pipeline';
import { ATOM_ADD_EVENT_NAME } from '#/components/pipeline/constants';

import { hashID } from '../pipeline/util';
import StageConfigDrawer from './stageConfigDrawer.vue';
import SubtaskConfigDrawer from './subtaskConfigDrawer.vue';
import TaskConfigDrawer from './taskConfigDrawer.vue';

const pipelineDataModel = defineModel('pipelineData', {
  type: Object,
  required: true,
});

const editable = ref<boolean>(true);
const cancelUserId = ref<string>('unknow');
const userName = ref<string>('unknow');
const matchRules = ref<string[]>([]);
const pipeline = ref();
const handlePipelineChange = (newPipeline: any) => {
  console.error('Pipeline changed:', newPipeline);
};

const handlePipelineClick = (event: any) => {
  switch (event.type) {
    case 'stage': {
      stageContentDrawerApi
        .setData({
          index: event.stageIndex,
          name: pipelineDataModel.value.stages[event.stageIndex]?.name,
          trigger:
            pipelineDataModel.value.stages[event.stageIndex]?.trigger || 'auto',
          timer_trigger_config:
            pipelineDataModel.value.stages[event.stageIndex]
              ?.timer_trigger_config,
        })
        .open();
      break;
    }
    case 'subtask': {
      subtaskContentDrawerApi
        .setData({
          mode: 'EDIT',
          stageIndex: event.stageIndex,
          taskIndex: event.taskIndex,
          index: event.subtaskIndex,
          name: pipelineDataModel.value.stages[event.stageIndex]?.tasks[
            event.taskIndex
          ]?.subtasks[event.subtaskIndex]?.name,
          config:
            pipelineDataModel.value.stages[event.stageIndex]?.tasks[
              event.taskIndex
            ]?.subtasks[event.subtaskIndex],
        })
        .open();

      break;
    }
    case 'task': {
      taskContentDrawerApi
        .setData({
          stageIndex: event.stageIndex,
          index: event.taskIndex,
          name: pipelineDataModel.value.stages[event.stageIndex]?.tasks[
            event.taskIndex
          ]?.name,
          skipCondition: pipelineDataModel.value.stages[event.stageIndex]
            ?.tasks[event.taskIndex]?.skip_condition ?? {
            is_enabled: false,
          },
        })
        .open();

      break;
    }
  }
};
const handleAddAtom = (event: any) => {
  subtaskContentDrawerApi
    .setData({
      mode: 'ADD',
      stageIndex: event.stage_idx,
      taskIndex: event.task_idx,
      index: event.subtask_idx + 1,
      name: '',
      config: {
        plugin: {
          name: '',
          input: [],
        },
      },
    })
    .open();
};

const handleStageChange = (stageIdx: number, newStage: any) => {
  // 只能修改步骤名称和触发方式
  pipelineDataModel.value.stages[stageIdx].name = newStage.name;
  pipelineDataModel.value.stages[stageIdx].trigger = newStage.trigger;
  if (newStage.trigger === 'timer') {
    pipelineDataModel.value.stages[stageIdx].timer_trigger_config =
      newStage.timer_trigger_config;
  }
};

const handleTaskChange = (stageIdx: number, taskIdx: number, newTask: any) => {
  // 只能修改任务名称和条件跳过
  pipelineDataModel.value.stages[stageIdx].tasks[taskIdx].name = newTask.name;
  pipelineDataModel.value.stages[stageIdx].tasks[taskIdx].skip_condition =
    newTask.skipCondition;
};

const handleSubtaskChange = (
  stageIdx: number,
  taskIdx: number,
  subtaskIdx: number,
  newSubtask: any,
  mode: 'ADD' | 'EDIT',
) => {
  // 新增子任务
  if (mode === 'ADD') {
    pipelineDataModel.value.stages[stageIdx].tasks[taskIdx].subtasks.push({
      id: `st_${hashID()}`,
      name: newSubtask.name,
      is_enabled: true,
      status: 'READY',
      plugin: newSubtask.config.plugin,
    });
  } else if (mode === 'EDIT') {
    const old =
      pipelineDataModel.value.stages[stageIdx].tasks[taskIdx].subtasks[
        subtaskIdx
      ];
    pipelineDataModel.value.stages[stageIdx].tasks[taskIdx].subtasks[
      subtaskIdx
    ] = {
      ...old,
      name: newSubtask.name,
      plugin: newSubtask.config.plugin,
    };
  }
};

const [SubtaskContentDrawer, subtaskContentDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: SubtaskConfigDrawer,
});

const [TaskContentDrawer, taskContentDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: TaskConfigDrawer,
});

const [StageContentDrawer, stageContentDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: StageConfigDrawer,
});

const handleAddStage = () => {
  pipeline.value?.handleAddStage({
    stageIndex: 0,
    isParallel: false,
    isFinally: false,
  });
};
</script>
<template>
  <div
    :class="{
      'scroll-container': pipelineDataModel.stages.length > 0,
    }"
  >
    <StageContentDrawer :handle-stage-change="handleStageChange" />
    <TaskContentDrawer :handle-task-change="handleTaskChange" />
    <SubtaskContentDrawer :handle-subtask-change="handleSubtaskChange" />
    <Pipeline
      ref="pipeline"
      class="ml-20 mt-10"
      :pipeline="pipelineDataModel"
      :editable="editable"
      :is-preview="false"
      :is-exec-detail="false"
      :is-latest-build="false"
      :can-skip-element="false"
      :cancel-user-id="cancelUserId"
      :user-name="userName"
      :match-rules="matchRules"
      :current-exec-count="1"
      :is-expand-all-matrix="true"
      @change="handlePipelineChange"
      @click="handlePipelineClick"
      @[ATOM_ADD_EVENT_NAME]="handleAddAtom"
    />
    <Button
      type="primary"
      @click="handleAddStage"
      class="ml-10"
      v-if="pipelineDataModel.stages.length === 0"
    >
      点击添加第一个步骤
    </Button>
  </div>
</template>
<style scoped>
.scroll-container::before {
  position: absolute;
  top: 64px;
  left: 30px;
  min-width: calc(100% - 30px);
  height: 0;
  content: '';
  border-top: 2px dashed #c3cdd7;
}
</style>
