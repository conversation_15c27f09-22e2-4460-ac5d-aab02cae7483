<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { Form, FormItem, Input } from 'ant-design-vue';

import { SkipCondition } from '#/components/skipCondition';

const props = defineProps({
  handleTaskChange: {
    type: Function,
    required: true,
  },
});

const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '保存',
  onConfirm() {
    props.handleTaskChange(data.value.stageIndex, data.value.index, {
      name: data.value.name,
      skipCondition: data.value.skipCondition,
    });
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});

const data = ref();
</script>
<template>
  <Drawer append-to-main title="任务详情" class="w-[900px]">
    <Form class="mt-4" :label-col="{ span: 3 }">
      <FormItem label="步骤序号" name="stageIndex">
        <Input
          placeholder="请输入步骤序号"
          :value="data.stageIndex + 1"
          disabled
        />
      </FormItem>
      <FormItem label="任务序号" name="taskIndex">
        <Input placeholder="请输入任务序号" :value="data.index + 1" disabled />
      </FormItem>
      <FormItem label="任务名称" name="taskName">
        <Input placeholder="请输入任务名称" v-model:value="data.name" />
      </FormItem>
      <FormItem label="条件跳过" name="skipCondition">
        <SkipCondition :value="data.skipCondition" />
      </FormItem>
    </Form>
  </Drawer>
</template>
