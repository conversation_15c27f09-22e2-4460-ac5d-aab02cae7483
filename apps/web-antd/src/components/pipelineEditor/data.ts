const CreateModuleServerInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '部署模式',
    key: 'deploy_mode',
    value: '',
    type: 'plain',
  },
  {
    name: '单机部署',
    key: 'all_in_one_hosts',
    value: '',
    type: 'plain',
  },
  {
    name: '模块信息',
    key: 'module_hosts',
    value: '',
    type: 'plain',
  },
];

const CreateRegionInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '初始分支',
    key: 'branch',
    value: '',
    type: 'plain',
  },
  {
    name: '部署模式',
    key: 'deploy_mode',
    value: '',
    type: 'plain',
  },
  {
    name: 'MySQL实例',
    key: 'mysql_infos',
    value: '',
    type: 'plain',
  },
  {
    name: 'Redis实例',
    key: 'redis_infos',
    value: '',
    type: 'plain',
  },
  {
    name: '单机部署',
    key: 'all_in_one_hosts',
    value: '',
    type: 'plain',
  },
  {
    name: '分模块部署',
    key: 'module_hosts',
    value: '',
    type: 'plain',
  },
  {
    name: '创建人',
    key: 'created_by',
    value: '',
    type: 'plain',
  },
];

const HostProvisionInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '扩容模块',
    key: 'modules',
    value: '',
    type: 'plain',
  },
  {
    name: '区服',
    key: 'data_version_info',
    value: '',
    type: 'plain',
  },
];

const HostLaunchInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '扩容模块',
    key: 'modules',
    value: '',
    type: 'plain',
  },
  {
    name: '区服',
    key: 'data_version_info',
    value: '',
    type: 'plain',
  },
];

const SugarChecklistInput = [
  {
    name: '巡检模版ID',
    key: 'id',
    value: '',
    type: 'plain',
  },
  {
    name: 'kwargs',
    key: 'kwargs',
    component: 'Textarea',
    value: {},
    type: 'plain',
  },
];

const ExecPipelineInput = [
  {
    name: '流程uid',
    key: 'uid',
    value: '',
    type: 'plain',
  },
  {
    name: '流程名称',
    key: 'name',
    value: '',
    type: 'plain',
  },
  {
    name: '超时时间(分钟)',
    key: 'timeout',
    component: 'InputNumber',
    value: 3,
    type: 'plain',
  },
  {
    name: '实例参数',
    key: 'instance_params',
    value: [],
    component: 'Textarea',
    type: 'plain',
  },
  {
    name: '实例参数key',
    key: 'instance_key',
    value: 'instance',
    type: 'plain',
  },
  {
    name: '参数',
    key: 'params',
    value: {},
    component: 'Textarea',
    type: 'plain',
  },
];

const HostTempOfflineInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '缩容模块',
    key: 'modules',
    value: '',
    type: 'plain',
  },
];

const HostOfflineInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '缩容模块',
    key: 'modules',
    value: '',
    type: 'plain',
  },
];

const CreateHostInput = [
  {
    name: '主机模板',
    key: 'template_id',
    value: '',
    type: 'plain',
  },
  {
    name: '主机名',
    key: 'hostname',
    value: '',
    type: 'plain',
  },
  {
    name: '原因',
    key: 'reason',
    value: '',
    type: 'plain',
  },
];

const DeleteHostInput = [
  {
    name: '实例名',
    key: 'instance_name',
    value: '',
    type: 'plain',
  },
  {
    name: '实例ID',
    key: 'instance_id',
    value: '',
    type: 'plain',
  },
  {
    name: '原因',
    key: 'reason',
    value: '',
    type: 'plain',
  },
];

const CreateRedisInput = [
  {
    name: '实例模板',
    key: 'template_id',
    value: '',
    type: 'plain',
  },
  {
    name: '实例名',
    key: 'instance_name',
    value: '',
    type: 'plain',
  },
  {
    name: '原因',
    key: 'reason',
    value: '',
    type: 'plain',
  },
];

const DeleteRedisInput = [
  {
    name: '实例名',
    key: 'instance_name',
    value: '',
    type: 'plain',
  },
  {
    name: '实例ID',
    key: 'instance_id',
    value: '',
    type: 'plain',
  },
  {
    name: '原因',
    key: 'reason',
    value: '',
    type: 'plain',
  },
];

const CreateSchemaInput = [
  {
    name: '源区服',
    key: 'src_region',
    value: '',
    type: 'plain',
  },
  {
    name: '目标区服',
    key: 'dst_region',
    value: '',
    type: 'plain',
  },
  {
    name: '执行类型',
    key: 'exec_type',
    value: '',
    type: 'plain',
  },
];

const GenerateSfInfoInput = [
  {
    name: '账号',
    key: 'account',
    value: '',
    type: 'plain',
  },
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '分支',
    key: 'branch',
    value: '',
    type: 'plain',
  },
  {
    name: '主机名',
    key: 'hostname',
    value: '',
    type: 'plain',
  },
  {
    name: 'MySQL信息',
    key: 'mysql_info',
    component: 'Textarea',
    value: {},
    type: 'plain',
  },
  {
    name: 'Redis信息',
    key: 'redis_info',
    component: 'Textarea',
    value: {},
    type: 'plain',
  },
];

const GenerateSfRecycleInfoInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
];

const EchoInput = [
  {
    name: '参数',
    key: 'variables',
    component: 'Textarea',
    value: {},
    type: 'plain',
  },
];

const SleepInput = [
  {
    name: '睡眠时间(秒)',
    key: 'duration',
    value: 3,
    type: 'plain',
    component: 'InputNumber',
  },
];

const ChangeModuleIsLoadInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '模块',
    key: 'modules',
    value: '',
    type: 'plain',
  },
  {
    name: '是否加载',
    key: 'is_load',
    value: true,
    type: 'plain',
    component: 'Switch',
  },
  {
    name: '实例代码',
    key: 'instance_code',
    value: '',
    type: 'plain',
  },
  {
    name: 'Schema代码',
    key: 'schema_code',
    value: '',
    type: 'plain',
  },
];

const WaitForPlayerToZeroInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '模块',
    key: 'modules',
    value: '',
    type: 'plain',
  },
  {
    name: '超时时间(分钟)',
    key: 'timeout',
    value: 3,
    type: 'plain',
    component: 'InputNumber',
  },
];

const ExecSqlTemplateInput = [
  {
    name: '模版代码',
    key: 'template_code',
    value: '',
    type: 'plain',
  },
  {
    name: 'Schema代码',
    key: 'schema_code',
    value: [],
    component: 'Textarea',
    type: 'plain',
  },
  {
    name: '备注',
    key: 'remark',
    value: '',
    type: 'plain',
  },
  {
    name: '参数',
    key: 'parameters',
    value: {},
    component: 'Textarea',
    type: 'render_map',
  },
];

const MuteAlertInput = [
  {
    name: '实例IP列表',
    key: 'instances',
    value: [],
    component: 'Textarea',
    type: 'plain',
  },
  {
    name: '持续时间(分钟)',
    key: 'duration',
    value: 3,
    type: 'plain',
    component: 'InputNumber',
  },
  {
    name: '原因',
    key: 'reason',
    value: '',
    type: 'plain',
  },
];

const UnMuteAlertInput = [
  {
    name: '屏蔽ID',
    key: 'mute_id',
    value: '',
    type: 'plain',
  },
];

const AddHostToMonitorGroupInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '监控组ID',
    key: 'group_id',
    value: 0,
    type: 'plain',
  },
  {
    name: '实例列表',
    key: 'instances',
    value: [],
    type: 'plain',
  },
];

const RemoveHostFromMonitorGroupInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '监控组ID',
    key: 'group_id',
    value: 0,
    type: 'plain',
  },
  {
    name: '实例列表',
    key: 'instance_ids',
    value: [],
    type: 'Textarea',
  },
];

const GetRegionResourceGroupInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
];

const AddHostToResourceGroupInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '资源组ID',
    key: 'group_id',
    value: '',
    type: 'plain',
  },
  {
    name: '实例列表',
    key: 'instances',
    value: [],
    component: 'Textarea',
    type: 'plain',
  },
];

const RemoveHostFromResourceGroupInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
  {
    name: '实例列表',
    key: 'instances',
    value: [],
    component: 'Textarea',
    type: 'plain',
  },
];

const GetRegionMonitorGroupInput = [
  {
    name: '区服',
    key: 'region',
    value: '',
    type: 'plain',
  },
];

export interface PluginInput {
  name: string;
  key: string;
  value: any;
  type: string;
  component?: string;
}

const pluginInput = {
  'sugar.checklist': SugarChecklistInput,
  'hotwheel.exec_pipeline': ExecPipelineInput,
  'region.create_modules_server': CreateModuleServerInput,
  'region.create_region': CreateRegionInput,
  'region.host_provision': HostProvisionInput,
  'region.host_launch': HostLaunchInput,
  'region.host_temp_offline': HostTempOfflineInput,
  'region.host_offline': HostOfflineInput,
  'cloudman.create_host': CreateHostInput,
  'cloudman.delete_host': DeleteHostInput,
  'cloudman.create_redis': CreateRedisInput,
  'cloudman.delete_redis': DeleteRedisInput,
  'cloudman.add_host_to_resource_group': AddHostToResourceGroupInput,
  'cloudman.remove_host_from_resource_group': RemoveHostFromResourceGroupInput,
  'cloudman.get_region_resource_group': GetRegionResourceGroupInput,
  'dbtool.create_schema': CreateSchemaInput,
  'dbtool.exec_sql_template': ExecSqlTemplateInput,
  'region.generate_sf_info': GenerateSfInfoInput,
  'region.generate_sf_recycle_info': GenerateSfRecycleInfoInput,
  'region.change_module_is_load': ChangeModuleIsLoadInput,
  'region.wait_for_player_to_zero': WaitForPlayerToZeroInput,
  'alert.mute': MuteAlertInput,
  'alert.unmute': UnMuteAlertInput,
  'alert.get_region_monitor_group': GetRegionMonitorGroupInput,
  'alert.add_host_to_monitor_group': AddHostToMonitorGroupInput,
  'alert.remove_host_from_monitor_group': RemoveHostFromMonitorGroupInput,
  'common.echo': EchoInput,
  'common.sleep': SleepInput,
} as Record<string, PluginInput[]>;

export { pluginInput };
