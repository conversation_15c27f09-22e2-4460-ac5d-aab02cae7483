<script lang="ts" setup>
import type { SelectProps } from 'ant-design-vue';
import type { SelectValue } from 'ant-design-vue/es/select';

import type { PluginInput } from './data';

import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { reactive, ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { EditOutlined } from '@ant-design/icons-vue';
import { Button, Form, FormItem, Input, message, Select } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { pluginInput } from './data';

const props = defineProps({
  handleSubtaskChange: {
    type: Function,
    required: true,
  },
});

const data = ref();

const gridOptions = reactive<VxeTableGridOptions<PluginInput>>({
  autoResize: true,
  columns: [
    {
      title: '参数名称',
      field: 'name',
      width: 150,
    },
    {
      title: '参数键名',
      field: 'key',
      width: 150,
    },
    {
      title: '参数值',
      field: 'value',
      formatter({ row }) {
        if (row.value && typeof row.value === 'object') {
          return JSON.stringify(row.value);
        } else if (row.value === '') {
          return '""';
        }
        return row.value;
      },
    },
    {
      title: '参数类型',
      field: 'type',
      width: 150,
      formatter({ row }) {
        switch (row.type) {
          case 'output': {
            return '输出参数';
          }
          case 'plain': {
            return '普通参数';
          }
          case 'ref': {
            return '引用';
          }
          case 'render_map': {
            return '渲染参数';
          }
          default: {
            return '未知类型';
          }
        }
      },
    },
    { title: '操作', width: 120, slots: { default: 'action' } },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
});

const outputGridOptions = reactive<VxeTableGridOptions<PluginInput>>({
  autoResize: true,
  columns: [
    {
      title: '参数名称',
      field: 'name',
      width: 150,
    },
    {
      title: '参数键名',
      field: 'key',
      width: 150,
    },
    {
      title: 'JSONPath(从插件输出中获取)',
      field: 'jsonpath',
    },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
});

const [ParamsGrid, gridApi] = useVbenVxeGrid(
  reactive({
    gridOptions,
  }),
);

const [OutputGrid, outputGridApi] = useVbenVxeGrid(
  reactive({
    gridOptions: outputGridOptions,
  }),
);

const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '保存',
  onConfirm() {
    props.handleSubtaskChange(
      data.value.stageIndex,
      data.value.taskIndex,
      data.value.index,
      {
        name: data.value.name,
        config: data.value.config,
      },
      data.value.mode,
    );
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      gridOptions.data = data.value.config.plugin.input;
      gridApi.setState({ gridOptions });
      outputGridOptions.data = data.value.config.plugin.output || [];
      outputGridApi.setState({ gridOptions: outputGridOptions });
    }
  },
});

const pluginOptions = ref<SelectProps['options']>([
  {
    label: '通用',
    options: [
      { label: 'http请求', value: 'common.http' },
      { label: 'debug打印', value: 'common.echo' },
      { label: 'sleep测试', value: 'common.sleep' },
    ],
  },
  {
    label: '标准运维',
    options: [{ label: '流水线执行', value: 'hotwheel.exec_pipeline' }],
  },
  {
    label: '巡检',
    options: [{ label: '巡检', value: 'sugar.checklist' }],
  },
  {
    label: '区服',
    options: [
      { label: '创建区服', value: 'region.create_region' },
      { label: '创建模块主机', value: 'region.create_modules_server' },
      { label: '主机扩容', value: 'region.host_provision' },
      { label: '主机上线', value: 'region.host_launch' },
      { label: '主机临时下线', value: 'region.host_temp_offline' },
      { label: '主机下线', value: 'region.host_offline' },
      { label: '生成私服信息', value: 'region.generate_sf_info' },
      { label: '生成私服回收信息', value: 'region.generate_sf_recycle_info' },
      { label: '修改模块是否加载', value: 'region.change_module_is_load' },
      { label: '等待玩家人数为0', value: 'region.wait_for_player_to_zero' },
    ],
  },
  {
    label: '云主机',
    options: [
      { label: '创建主机', value: 'cloudman.create_host' },
      { label: '创建Redis', value: 'cloudman.create_redis' },
      { label: '删除主机', value: 'cloudman.delete_host' },
      { label: '删除Redis', value: 'cloudman.delete_redis' },
      {
        label: '获取区服资源组',
        value: 'cloudman.get_region_resource_group',
      },
      {
        label: '添加主机到资源组',
        value: 'cloudman.add_host_to_resource_group',
      },
      {
        label: '从资源组移除主机',
        value: 'cloudman.remove_host_from_resource_group',
      },
    ],
  },
  {
    label: '数据库',
    options: [
      {
        label: '创建数据库Schema',
        value: 'dbtool.create_schema',
      },
      {
        label: '执行SQL模版',
        value: 'dbtool.exec_sql_template',
      },
    ],
  },
  {
    label: '告警',
    options: [
      { label: '屏蔽告警', value: 'alert.mute' },
      { label: '取消屏蔽告警', value: 'alert.unmute' },
      { label: '获取区服监控组', value: 'alert.get_region_monitor_group' },
      { label: '添加主机到监控组', value: 'alert.add_host_to_monitor_group' },
      {
        label: '从监控组移除主机',
        value: 'alert.remove_host_from_monitor_group',
      },
    ],
  },
]);

const handlePluginSelectionChange = (value: SelectValue) => {
  const pInput = pluginInput[value as string] || [];
  data.value.config.plugin.input = pInput;
  gridOptions.data = pInput;
  gridApi.setState({ gridOptions });
  data.value.name = pluginOptions.value
    ?.reduce((acc, item) => {
      acc.push(...item.options);
      return acc;
    }, [])
    .find((item: any) => item.value === value)?.label;
};

const defaultSchema: VbenFormSchema[] = [
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'name',
    label: '参数名称',
    rules: 'required',
    disabled: true,
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'key',
    label: '参数键名',
    rules: 'required',
    disabled: true,
  },
  {
    component: 'Input',
    componentProps: {
      class: 'w-full',
      placeholder: '请输入',
    },
    fieldName: 'value',
    label: '参数值',
    rules: 'required',
  },
  {
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '普通参数', value: 'plain' },
        { label: '引用', value: 'ref' },
        { label: '渲染参数', value: 'render_map' },
      ],
    },
    fieldName: 'type',
    label: '参数类型',
    rules: 'required',
  },
];

const inputFormSchema = ref<VbenFormSchema[]>();

const [InputForm, formApi] = useVbenForm(
  reactive({
    schema: inputFormSchema,
    showDefaultActions: false,
  }),
);

const [InputModal, modalApi] = useVbenModal({
  fullscreenButton: false,
  confirmText: '保存',
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.getValues().then((values) => {
      const modalData = modalApi.getData<PluginInput>();
      if (modalData.component === 'Textarea') {
        try {
          values.value = JSON.parse(values.value);
        } catch {
          message.error('JSON格式错误');
          return;
        }
      }
      // 修改对应参数的value和type
      data.value.config.plugin.input = gridOptions.data?.map((item) => {
        if (item.key === modalData.key) {
          return {
            ...item,
            value: values.value,
            type: values.type,
          } as PluginInput;
        }
        return item;
      });
      gridOptions.data = data.value.config.plugin.input;
      gridApi.setState({ gridOptions });
      modalApi.close();
    });
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const schema = cloneDeep(defaultSchema);
      const data = modalApi.getData<PluginInput>();
      if (typeof data.value === 'object') {
        data.value = JSON.stringify(data.value);
      }
      if (modalApi.getData<PluginInput>().component && schema[2]) {
        schema[2].component =
          modalApi.getData<PluginInput>().component || 'Input';
        schema[2].componentProps = {
          rows: 4,
        };
      }
      inputFormSchema.value = schema;
      formApi.setValues(modalApi.getData<PluginInput>());
    }
  },
  title: '预览表单',
});

const editRow = (row: PluginInput) => {
  modalApi.setData(row).open();
};
</script>
<template>
  <Drawer append-to-main title="子任务详情" class="w-[1500px]">
    <InputModal>
      <InputForm />
    </InputModal>
    <Form class="mt-4" :label-col="{ span: 3 }">
      <FormItem label="子任务序号" name="index">
        <Input
          placeholder="请输入子任务序号"
          :value="data.index + 1"
          disabled
        />
      </FormItem>
      <FormItem label="子任务名称" name="name">
        <Input placeholder="请输入子任务名称" v-model:value="data.name" />
      </FormItem>
      <FormItem label="子任务插件" name="plugin">
        <Select
          placeholder="请选择子任务插件"
          v-model:value="data.config.plugin.name"
          :options="pluginOptions"
          @change="handlePluginSelectionChange"
        />
      </FormItem>
      <FormItem label="输入参数" name="input">
        <ParamsGrid>
          <template #action="{ row }">
            <Button type="link" @click="editRow(row)">
              <template #icon>
                <EditOutlined />
              </template>
              编辑
            </Button>
          </template>
        </ParamsGrid>
      </FormItem>
      <FormItem label="输出参数" name="output">
        <OutputGrid />
      </FormItem>
    </Form>
  </Drawer>
</template>
