<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import {
  Checkbox,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  Select,
  Switch,
} from 'ant-design-vue';

const props = defineProps({
  handleStageChange: {
    type: Function,
    required: true,
  },
});

const timerConfig = reactive({
  is_enabled: false,
  trigger_type: 'fixed_time',
  fixed_time: undefined,
  fixed_delay: 0,
  fixed_delay_unit: 's',
  is_ref: false,
  ref_variable_key: '',
});

const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '保存',
  onConfirm() {
    const newStage = {
      name: data.value.name,
      trigger: data.value.trigger,
    };
    const timerTriggerConfig = {
      trigger_type: timerConfig.trigger_type,
      fixed_time: timerConfig.fixed_time,
      fixed_delay: timerConfig.fixed_delay,
      fixed_delay_unit: timerConfig.fixed_delay_unit,
      is_ref: timerConfig.is_ref,
      ref_variable_key: timerConfig.ref_variable_key,
    };

    if (timerConfig.is_enabled) {
      props.handleStageChange(data.value.index, {
        ...newStage,
        timer_trigger_config: timerTriggerConfig,
      });
    } else {
      props.handleStageChange(data.value.index, newStage);
    }

    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      if (data.value.trigger === 'timer') {
        timerConfig.is_enabled = true;
        timerConfig.trigger_type = data.value.timer_trigger_config.trigger_type;
        timerConfig.fixed_time = data.value.timer_trigger_config.fixed_time;
        timerConfig.fixed_delay = data.value.timer_trigger_config.fixed_delay;
        timerConfig.fixed_delay_unit =
          data.value.timer_trigger_config.fixed_delay_unit;
        timerConfig.is_ref = data.value.timer_trigger_config.is_ref;
        timerConfig.ref_variable_key =
          data.value.timer_trigger_config.ref_variable_key;
      }
    }
  },
});

const triggerOptions = ref([
  { label: '自动', value: 'auto' },
  { label: '手动', value: 'manual' },
  { label: '定时', value: 'timer' },
]);

const timerTriggerOptions = ref([
  { label: '指定时间触发', value: 'fixed_time' },
  { label: '延迟触发', value: 'fixed_delay' },
]);

const delayUnitOptions = ref([
  { label: '秒', value: 's' },
  { label: '分钟', value: 'm' },
  { label: '小时', value: 'h' },
  { label: '天', value: 'd' },
]);

const data = ref();
</script>
<template>
  <Drawer append-to-main title="步骤详情" class="w-[900px]">
    <Form class="mt-4" :label-col="{ span: 3 }">
      <FormItem label="步骤序号" name="stageIndex">
        <Input placeholder="请输入步骤序号" :value="data.index + 1" disabled />
      </FormItem>
      <FormItem label="步骤名称" name="stageName">
        <Input placeholder="请输入步骤名称" v-model:value="data.name" />
      </FormItem>
      <FormItem label="触发方式" name="trigger">
        <Select v-model:value="data.trigger" :options="triggerOptions" />
      </FormItem>
      <FormItem
        v-if="data.trigger === 'timer'"
        label="定时触发配置"
        name="timerTrigger"
      >
        <Switch v-model:checked="timerConfig.is_enabled" />
      </FormItem>
      <template v-if="data.trigger === 'timer' && timerConfig.is_enabled">
        <FormItem label="定时类型">
          <Select
            v-model:value="timerConfig.trigger_type"
            :options="timerTriggerOptions"
          />
        </FormItem>
        <FormItem
          label="固定时间"
          v-if="timerConfig.trigger_type === 'fixed_time'"
        >
          <DatePicker
            class="w-[300px]"
            v-if="!timerConfig.is_ref"
            v-model:value="timerConfig.fixed_time"
            placeholder="请输入固定时间"
            show-time
            show-now
          />
          <Input
            class="w-[300px]"
            v-else-if="timerConfig.is_ref"
            v-model:value="timerConfig.ref_variable_key"
            placeholder="请输入变量名称"
          />
          <Checkbox
            v-model:checked="timerConfig.is_ref"
            class="ml-2"
            style="line-height: 32px"
          >
            引用变量
          </Checkbox>
        </FormItem>
        <FormItem
          label="延迟时间"
          v-if="timerConfig.trigger_type === 'fixed_delay'"
        >
          <InputNumber
            class="w-[300px]"
            v-if="!timerConfig.is_ref"
            v-model:value="timerConfig.fixed_delay"
            placeholder="请输入延迟时间"
          >
            <template #addonAfter>
              <Select
                class="w-[100px]"
                v-model:value="timerConfig.fixed_delay_unit"
                :options="delayUnitOptions"
              />
            </template>
          </InputNumber>
          <Input
            v-else-if="timerConfig.is_ref"
            class="w-[300px]"
            v-model:value="timerConfig.ref_variable_key"
            placeholder="请输入变量名称"
          >
            <template #addonAfter>
              <Select
                class="w-[100px]"
                v-model:value="timerConfig.fixed_delay_unit"
                :options="delayUnitOptions"
              />
            </template>
          </Input>

          <Checkbox
            v-model:checked="timerConfig.is_ref"
            class="ml-2"
            style="line-height: 32px"
          >
            引用变量
          </Checkbox>
        </FormItem>
      </template>
    </Form>
  </Drawer>
</template>
