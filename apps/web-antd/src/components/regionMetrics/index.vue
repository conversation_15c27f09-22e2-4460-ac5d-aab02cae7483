<script lang="ts" setup>
import { ref } from 'vue';

import { TabPane, Tabs } from 'ant-design-vue';

import { RefreshButton } from '#/components/refresh';

import DataInfo from './dataInfo.vue';
import HealthyStatus from './healthyStatus.vue';
import OnlineNum from './onlineNum.vue';
import PatchInfo from './patchInfo.vue';
import ServiceInfo from './serviceInfo.vue';

const props = defineProps<{
  region: string;
}>();

const activeKey = ref('1');
const onlineNumRef = ref();
const healthyStatusRef = ref();
const serviceInfoRef = ref();
const dataInfoRef = ref();
const patchInfoRef = ref();
const getRefreshFunction = () => {
  switch (activeKey.value) {
    case '1': {
      onlineNumRef.value.refreshFunction();
      return;
    }
    case '2': {
      healthyStatusRef.value.refreshFunction();
      return;
    }
    case '3': {
      serviceInfoRef.value.refreshFunction();
      return;
    }
    case '4': {
      dataInfoRef.value.refreshFunction();
      return;
    }
    case '5': {
      patchInfoRef.value.refreshFunction();
      return;
    }
    default: {
      return () => {};
    }
  }
};
</script>

<template>
  <Tabs v-model:active-key="activeKey" destroy-inactive-tab-pane>
    <template #leftExtra>
      <RefreshButton
        :refresh="getRefreshFunction"
        :default-refresh-interval="15"
        class="mr-4"
      />
    </template>
    <TabPane key="1" tab="在线人数">
      <OnlineNum :region="props.region" ref="onlineNumRef" />
    </TabPane>
    <TabPane key="2" tab="进程状态">
      <HealthyStatus :region="props.region" ref="healthyStatusRef" />
    </TabPane>
    <TabPane key="3" tab="服务信息">
      <ServiceInfo :region="props.region" ref="serviceInfoRef" />
    </TabPane>
    <TabPane key="4" tab="数值信息">
      <DataInfo :region="props.region" ref="dataInfoRef" />
    </TabPane>
    <TabPane key="5" tab="patch信息">
      <PatchInfo :region="props.region" ref="patchInfoRef" />
    </TabPane>
  </Tabs>
</template>
