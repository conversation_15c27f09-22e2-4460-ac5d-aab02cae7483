<script lang="ts" setup>
import type { EChartsOption, EchartsUIType } from '@vben/plugins/echarts';

import type { GetRegionHealthyInfo } from '#/api/region/type';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getRegionHealthyInfoApi } from '#/api';

const props = withDefaults(
  defineProps<{
    region: string;
  }>(),
  {
    region: '',
  },
);
const chartRef = ref<EchartsUIType>();
const { renderEcharts, getChartInstance } = useEcharts(chartRef);

const sources = ref<GetRegionHealthyInfo[]>([]);

const getHealthyStatus = () => {
  return getRegionHealthyInfoApi(props.region).then((res) => {
    sources.value = res.list;
  });
};

const getEchartOptions = () => {
  const options: EChartsOption = {
    legend: {},
    tooltip: {},
    grid: {
      left: '2%',
      right: '2%',
      bottom: '6%',
      width: '98%',
    },
    dataset: {
      dimensions: [
        'module',
        'healthy_count',
        'un_healthy_count',
        'total_count',
      ],
      source: sources.value,
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {},
    series: [
      {
        type: 'bar',
        color: '#87d068',
        name: '正常',
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            let healthyCount = 0;
            let totalCount = 0;
            if (
              params.data &&
              typeof params.data === 'object' &&
              'healthy_count' in params.data &&
              'total_count' in params.data
            ) {
              healthyCount = (params.data as any).healthy_count as number;
              totalCount = (params.data as any).total_count as number;
            }

            return `${healthyCount}/${totalCount}`;
          },
        },
      },
    ],
  };
  if (
    sources.value.some((item) => item.un_healthy_count !== 0) &&
    options.series
  ) {
    (options.series as any[]).push({
      type: 'bar',
      color: '#f50',
      name: '异常',
      label: {
        show: true,
        color: '#f50',
        position: 'top',
        formatter: (params: any) => {
          let unhealthyCount = 0;
          let totalCount = 0;
          if (
            params.data &&
            typeof params.data === 'object' &&
            'un_healthy_count' in params.data &&
            'total_count' in params.data
          ) {
            unhealthyCount = (params.data as any).un_healthy_count as number;
            totalCount = (params.data as any).total_count as number;
          }

          if (unhealthyCount === 0) {
            return '';
          }

          return `${unhealthyCount}/${totalCount}`;
        },
      },
    });
  }
  return options;
};
const renderChart = () => {
  renderEcharts(getEchartOptions());
};

const refreshFunction = () => {
  getHealthyStatus().then(() => {
    getChartInstance()?.setOption(getEchartOptions());
  });
};

onMounted(async () => {
  getHealthyStatus().then(() => {
    renderChart();
  });
});
defineExpose({
  refreshFunction,
});
</script>

<template>
  <div>
    <EchartsUI ref="chartRef" class="mt-10" style="height: 400px" />
  </div>
</template>
