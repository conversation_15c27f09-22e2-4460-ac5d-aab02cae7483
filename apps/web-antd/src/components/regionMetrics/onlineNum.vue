<script lang="ts" setup>
import type { EChartsOption, EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getRegionPlayerNumApi } from '#/api';

const props = withDefaults(
  defineProps<{
    region: string;
    step?: number;
  }>(),
  {
    region: '',
    step: 30,
  },
);
const chartRef = ref<EchartsUIType>();
const { renderEcharts, getChartInstance } = useEcharts(chartRef);
const timeSerires = ref<string[]>([]);
const playerNum = ref<number[]>([]);
const ufightPlayerNum = ref<number[]>([]);
const fightPlayerNum = ref<number[]>([]);

const getPlayerNum = () => {
  return getRegionPlayerNumApi(props.region, props.step).then((res) => {
    timeSerires.value = [];
    playerNum.value = [];
    ufightPlayerNum.value = [];
    fightPlayerNum.value = [];
    res.list.forEach((item) => {
      timeSerires.value.push(item.time);
      playerNum.value.push(item.user_count);
      ufightPlayerNum.value.push(item.ufight_user_count);
      fightPlayerNum.value.push(item.fight_user_count);
    });
  });
};

const getEchartOptions = () => {
  const options = {
    grid: {
      left: '2%',
      right: '4%',
      bottom: '6%',
      width: '98%',
    },
    legend: {
      data: ['总在线人数'],
    },
    tooltip: {
      trigger: 'none',
      axisPointer: {
        type: 'cross',
      },
    },
    xAxis: {
      type: 'category',
      data: timeSerires.value,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '总在线人数',
        data: playerNum.value,
        type: 'line',
        label: {
          show: true,
          position: 'top',
        },
      },
    ],
  };
  if (ufightPlayerNum.value.every((item) => item !== 0)) {
    options.series.push({
      name: '小游戏在线人数',
      data: ufightPlayerNum.value,
      type: 'line',
      label: {
        show: true,
        position: 'top',
      },
    });
    options.legend.data.push('小游戏在线人数');
  }
  if (fightPlayerNum.value.every((item) => item !== 0)) {
    options.series.push({
      name: 'fight在线人数',
      data: fightPlayerNum.value,
      type: 'line',
      label: {
        show: true,
        position: 'top',
      },
    });
    options.legend.data.push('fight在线人数');
  }
  return options as EChartsOption;
};

const renderChart = () => {
  renderEcharts(getEchartOptions());
};

onMounted(async () => {
  getPlayerNum().then(() => {
    renderChart();
  });
});

const refreshFunction = () => {
  getPlayerNum().then(() => {
    getChartInstance()?.setOption(getEchartOptions());
  });
};
defineExpose({
  refreshFunction,
});
</script>

<template>
  <div>
    <EchartsUI ref="chartRef" class="mt-10" style="height: 400px" />
  </div>
</template>
