<script lang="ts" setup>
import type { EChartsOption, EchartsUIType } from '@vben/plugins/echarts';

import type { GetRegionInfo, GetRegionInfoSummary } from '#/api/region/type';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons-vue';
import {
  RadioButton,
  RadioGroup,
  Row,
  Statistic,
  TypographyTitle,
} from 'ant-design-vue';

import { getRegionInfoApi, getRegionInfoSummaryApi } from '#/api';

const props = withDefaults(
  defineProps<{
    region: string;
  }>(),
  {
    region: '',
  },
);

const graphType = ref('MONITOR_GIT_COMMIT_ID');
const labelMetrics = [
  {
    label: '配置文件MD5',
    value: 'MONITOR_CONFIG_HASH',
    isLabelVal: false,
  },
  {
    label: '提交ID',
    value: 'MONITOR_GIT_COMMIT_ID',
    isLabelVal: true,
  },
  {
    label: '编译时间',
    value: 'MONITOR_BUILD_TIME',
    isLabelVal: true,
  },
  {
    label: '分支名',
    value: 'MONITOR_BRANCH_NAME',
    isLabelVal: true,
  },
];
const chartRef = ref<EchartsUIType>();
const { renderEcharts, getChartInstance } = useEcharts(chartRef);
const infoMetrics = ref<GetRegionInfo[]>([]);

const getInfoMetrics = () => {
  if (!graphType.value) {
    return;
  }

  return getRegionInfoApi(props.region, {
    metric_name: graphType.value,
    is_label_val: labelMetrics.find((item) => item.value === graphType.value)!
      .isLabelVal,
  }).then((res) => {
    infoMetrics.value = res.list;
  });
};

const renderChart = () => {
  renderEcharts(getGraphOptions());
};

const getGraphOptions = () => {
  const series: any[] = [];
  const valueModuleMap = new Map<string, string[]>();
  infoMetrics.value.forEach((item) => {
    (item.value_count || []).forEach((valueItem) => {
      if (!valueModuleMap.has(valueItem.value)) {
        valueModuleMap.set(valueItem.value, []);
      }
      valueModuleMap.get(valueItem.value)!.push(item.module);
    });
  });
  const allModules = infoMetrics.value.map((item) => item.module);
  valueModuleMap.forEach((modules, value) => {
    series.push({
      name: value,
      type: 'bar',
      stack: '总量',
      data: allModules.map((module) => {
        if (!modules.includes(module)) {
          return 0;
        }
        return infoMetrics.value.find((item) => item.module === module)!
          .total_count;
      }),
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          if (params.value === 0) {
            return '';
          }
          const totalCount = infoMetrics.value.find(
            (item) => item.module === params.name,
          )!.total_count;
          return `${params.value}/${totalCount}`;
        },
      },
    });
  });

  return {
    legend: {},
    tooltip: {},
    grid: {
      left: '2%',
      right: '2%',
      bottom: '7%',
      width: '98%',
    },
    xAxis: {
      type: 'category',
      data: allModules,
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
    },
    series,
  } as EChartsOption;
};

watch(
  () => graphType.value,
  () => {
    getInfoMetrics()?.then(() => {
      renderChart();
    });
  },
);

const infoSummary = ref<GetRegionInfoSummary[]>([]);

const getStatusText = (status: string) => {
  switch (status) {
    case 'consistent': {
      return '一致';
    }
    case 'inconsistent': {
      return '不一致';
    }
    case 'no_report': {
      return '未上报';
    }
    default: {
      return '未知';
    }
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'consistent': {
      return '#87d068';
    }
    case 'inconsistent': {
      return '#f50';
    }
    case 'no_report': {
      return '#999';
    }
    default: {
      return '#999';
    }
  }
};

const getInfoSummary = () => {
  return getRegionInfoSummaryApi(props.region, {
    metrics_info: labelMetrics.map((item) => ({
      metric_name: item.value,
      is_label_val: item.isLabelVal,
    })),
  }).then((res) => {
    infoSummary.value = res.list;
  });
};

const refreshFunction = () => {
  Promise.all([getInfoSummary(), getInfoMetrics()]).then(() => {
    getChartInstance()?.setOption(getGraphOptions());
  });
};

onMounted(async () => {
  Promise.all([getInfoSummary(), getInfoMetrics()]).then(() => {
    renderChart();
  });
});

defineExpose({
  refreshFunction,
});
</script>

<template>
  <Row type="flex">
    <TypographyTitle :level="5" class="mt-4">
      快速检查版本是否一致:
    </TypographyTitle>
    <template v-for="item in labelMetrics" :key="item.value">
      <Statistic
        :title="item.label"
        :value="
          getStatusText(
            infoSummary.find((i) => i.metric_name === item.value)?.status ?? '',
          )
        "
        :value-style="{
          color: getStatusColor(
            infoSummary.find((i) => i.metric_name === item.value)?.status ?? '',
          ),
        }"
        @click="
          () => {
            graphType = item.value;
          }
        "
        style="cursor: pointer"
        class="ml-6"
      >
        <template #suffix>
          <template
            v-if="
              infoSummary.find((i) => i.metric_name === item.value)?.status ===
              'consistent'
            "
          >
            <CheckCircleOutlined />
          </template>
          <template
            v-else-if="
              infoSummary.find((i) => i.metric_name === item.value)?.status ===
              'inconsistent'
            "
          >
            <CloseCircleOutlined />
          </template>
          <template v-else>
            <MinusCircleOutlined />
          </template>
        </template>
      </Statistic>
    </template>
  </Row>
  <RadioGroup v-model:value="graphType" class="mt-4">
    <RadioButton value="MONITOR_GIT_COMMIT_ID">提交ID</RadioButton>
    <RadioButton value="MONITOR_BUILD_TIME">编译时间</RadioButton>
    <RadioButton value="MONITOR_BRANCH_NAME">分支名</RadioButton>
    <RadioButton value="MONITOR_CONFIG_HASH">配置文件MD5</RadioButton>
  </RadioGroup>
  <EchartsUI ref="chartRef" class="mt-10" />
</template>
