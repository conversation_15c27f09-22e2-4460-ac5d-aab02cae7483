<script lang="ts" setup>
import type { EChartsOption, EchartsUIType } from '@vben/plugins/echarts';

import type { GetRegionInfo } from '#/api/region/type';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getRegionInfoApi } from '#/api';

const props = withDefaults(
  defineProps<{
    region: string;
  }>(),
  {
    region: '',
  },
);

const chartRef = ref<EchartsUIType>();
const { renderEcharts, getChartInstance } = useEcharts(chartRef);
const infoMetrics = ref<GetRegionInfo[]>([]);

const getInfoMetrics = () => {
  return getRegionInfoApi(props.region, {
    metric_name: 'MONITOR_INSTALLED_PATCH_VERSION',
    is_label_val: true,
  }).then((res) => {
    infoMetrics.value = res.list;
  });
};

const renderChart = () => {
  renderEcharts(getGraphOptions());
};

const getGraphOptions = () => {
  const series: any[] = [];
  const valueModuleMap = new Map<string, string[]>();
  infoMetrics.value.forEach((item) => {
    (item.value_count || []).forEach((valueItem) => {
      if (!valueModuleMap.has(valueItem.value)) {
        valueModuleMap.set(valueItem.value, []);
      }
      valueModuleMap.get(valueItem.value)!.push(item.module);
    });
  });
  const allModules = infoMetrics.value.map((item) => item.module);
  valueModuleMap.forEach((modules, value) => {
    series.push({
      name: value,
      type: 'bar',
      stack: '总量',
      data: allModules.map((module) => {
        if (!modules.includes(module)) {
          return 0;
        }
        return infoMetrics.value.find((item) => item.module === module)!
          .total_count;
      }),
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          if (params.value === 0) {
            return '';
          }
          const totalCount = infoMetrics.value.find(
            (item) => item.module === params.name,
          )!.total_count;
          return `${params.value}/${totalCount}`;
        },
      },
    });
  });

  return {
    legend: {},
    tooltip: {},
    grid: {
      left: '2%',
      right: '2%',
      bottom: '7%',
      width: '98%',
    },
    xAxis: {
      type: 'category',
      data: allModules,
      axisTick: {
        alignWithLabel: true,
      },
    },
    yAxis: {
      type: 'value',
    },
    series,
  } as EChartsOption;
};

const refreshFunction = () => {
  getInfoMetrics()?.then(() => {
    getChartInstance()?.setOption(getGraphOptions());
  });
};

onMounted(async () => {
  getInfoMetrics()?.then(() => {
    renderChart();
  });
});

defineExpose({
  refreshFunction,
});
</script>

<template>
  <EchartsUI ref="chartRef" class="mt-10" style="height: 400px" />
</template>
