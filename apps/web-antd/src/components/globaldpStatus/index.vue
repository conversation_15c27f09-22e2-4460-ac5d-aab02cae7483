<script lang="ts" setup>
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { queryDpHostData } from '#/api/global_dp/type';

import { ref } from 'vue';

import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons-vue';
import { Divider, Row, Statistic, Tag, TypographyTitle } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getGlobalDispatchHostInfoApi } from '#/api/global_dp/global_dp';

const props = defineProps<{
  name: string;
}>();

const infoSummary = ref([
  {
    label: '配置文件MD5',
    value: 'config_hash',
    status: 'unknown',
  },
  {
    label: '提交ID',
    value: 'commit_id',
    status: 'unknown',
  },
  {
    label: '编译时间',
    value: 'build_time',
    status: 'unknown',
  },
  {
    label: '分支',
    value: 'branch',
    status: 'unknown',
  },
]);

const getStatusText = (status: string) => {
  switch (status) {
    case 'consistent': {
      return '一致';
    }
    case 'inconsistent': {
      return '不一致';
    }
    case 'no_report': {
      return '未上报';
    }
    default: {
      return '未知';
    }
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'consistent': {
      return '#87d068';
    }
    case 'inconsistent': {
      return '#f50';
    }
    case 'no_report': {
      return '#999';
    }
    default: {
      return '#999';
    }
  }
};

const gridOptions: VxeTableGridOptions<queryDpHostData> = {
  showOverflow: false,
  columns: [
    {
      title: '主机名',
      field: 'hostname',
      width: 250,
    },
    {
      title: 'IP地址',
      field: 'ip',
    },
    {
      title: '虚拟地址',
      field: 'vaddr',
    },
    {
      title: '健康状态',
      field: 'is_healthy',
      slots: { default: 'is_healthy' },
    },
    {
      title: '分支',
      field: 'branch',
      formatter: ({ cellValue }) => {
        return cellValue || '-';
      },
    },
    {
      title: '配置文件MD5',
      field: 'config_hash',
    },
    {
      title: 'commit_id',
      field: 'commit_id',
    },
    {
      title: 'build_time',
      field: 'build_time',
      width: 200,
    },
  ],
  exportConfig: {},
  minHeight: '300',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getGlobalDispatchHostInfoApi(props.name, {
          page: 1,
          size: 999, // 因为一级DP主机数量最多不会超过100台,所以这里hardcode size为999
        }).then((data) => {
          const res = {
            total: data.total,
            items: data.list,
          };
          const firstValue = data.list[0] || ({} as queryDpHostData);
          infoSummary.value = infoSummary.value.map((item) => {
            return {
              ...item,
              status: data.list.every((d) => {
                const key = item.value as keyof queryDpHostData;
                return d[key] === firstValue[key];
              })
                ? 'consistent'
                : 'inconsistent',
            };
          });
          return res;
        });
      },
    },
  },
};

const [Grid] = useVbenVxeGrid({
  gridOptions,
});
</script>
<template>
  <div>
    <Divider>一级DP主机列表</Divider>
    <Row type="flex">
      <TypographyTitle :level="5" class="mt-4">
        快速检查版本是否一致:
      </TypographyTitle>
      <template v-for="item in infoSummary" :key="item.value">
        <Statistic
          :title="item.label"
          :value="getStatusText(item.status)"
          :value-style="{
            color: getStatusColor(item.status),
          }"
          class="ml-6"
        >
          <template #suffix>
            <template v-if="item.status === 'consistent'">
              <CheckCircleOutlined />
            </template>
            <template v-else-if="item.status === 'inconsistent'">
              <CloseCircleOutlined />
            </template>
            <template v-else>
              <MinusCircleOutlined />
            </template>
          </template>
        </Statistic>
      </template>
    </Row>
    <Grid>
      <template #is_healthy="{ row }">
        <Tag color="green" v-if="row.is_healthy"> 正常 </Tag>
        <Tag color="error" v-else> 异常 </Tag>
      </template>
    </Grid>
  </div>
</template>
