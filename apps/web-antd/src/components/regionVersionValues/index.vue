<script lang="ts" setup>
import type { TableColumnType } from 'ant-design-vue';

import { computed, ref, watch } from 'vue';

import { Input, Table } from 'ant-design-vue';

import { getRegionDataApi } from '#/api/region/region';

interface VersionValue {
  data_version: string;
  res_version: string;
  activity_version: string;
  bp_activity_version: string;
}

interface Props {
  region?: string;
}

const props = withDefaults(defineProps<Props>(), {
  region: '',
});
const modelValue = defineModel<VersionValue>('value', { required: true });
const loading = ref<boolean>(false);

// 表格列配置
const columns: TableColumnType[] = [
  { title: '名称', dataIndex: 'name', key: 'name' },
  { title: '值', dataIndex: 'value', key: 'value' },
];

const getRegionDataInfo = async (region: string) => {
  if (!region) {
    return;
  }
  loading.value = true;
  await getRegionDataApi(region).then((res) => {
    loading.value = false;
    modelValue.value = {
      data_version: res.data_version,
      res_version: res.res_version,
      activity_version:
        res.activity_version === '0' ? '' : res.activity_version,
      bp_activity_version:
        res.bp_activity_version === '0' ? '' : res.bp_activity_version,
    };
  });
};

const keyName = {
  data_version: '数值版本',
  res_version: '服务器资源版本',
  activity_version: '活动数值ACTIVITY版本',
  bp_activity_version: '活动数值BP版本',
};

const tableData = computed(() => {
  const res = (Object.keys(modelValue.value) as Array<keyof VersionValue>).map(
    (key) => {
      return {
        name: keyName[key],
        key,
        value: modelValue.value[key],
      };
    },
  );
  return res;
});

watch(
  () => props.region,
  async (newVal, old) => {
    if (newVal === old || !newVal) {
      return;
    }
    await getRegionDataInfo(newVal);
  },
  { immediate: true },
);
</script>

<template>
  <Table
    :columns="columns"
    :data-source="tableData"
    bordered
    size="small"
    :pagination="false"
    :loading="loading"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'name'">
        {{ record.name }}
      </template>
      <template v-if="column.key === 'value'">
        <Input
          v-model:value="record.value"
          @change="
            modelValue[record.key as keyof VersionValue] =
              $event.target.value || ''
          "
        />
      </template>
    </template>
  </Table>
</template>
