<script lang="ts" setup>
import type { SelectOption } from '@vben/types';

import type { NotificationSettings } from '#/api/settings/type';

import { onMounted, ref } from 'vue';

import { Select, Switch } from 'ant-design-vue';

import { getNotificationSettingsApi } from '#/api/settings/settings';

interface notificationConfig {
  is_enabled: boolean;
  user_ids: string[];
  chat_ids: string[];
}

const value = defineModel<notificationConfig>('value', { required: true });

const notificationSettings = ref<NotificationSettings>();
const usersOptions = ref<SelectOption[]>([]);
const chatsOptions = ref<SelectOption[]>([]);

onMounted(() => {
  getNotificationSettingsApi()
    .then((res) => {
      notificationSettings.value = res;
      usersOptions.value = res.users.map((item) => ({
        label: item.name,
        value: item.user_id,
      }));
      chatsOptions.value = res.chats?.map((item) => ({
        label: item.name,
        value: item.chat_id,
      }));
    })
    .catch((error) => {
      console.error('Failed to get notification settings:', error);
      usersOptions.value = [];
      chatsOptions.value = [];
    });
});
</script>

<template>
  <div>
    <div>
      <Switch
        v-model:checked="value.is_enabled"
        un-checked-children="未开启"
        checked-children="已开启"
      />
    </div>
    <div v-show="value.is_enabled" class="mt-2">
      通知用户:
      <Select
        class="ml-2 w-[300px]"
        mode="multiple"
        placeholder="请选择用户"
        v-model:value="value.user_ids"
        :options="usersOptions"
      />
    </div>
    <div v-show="value.is_enabled" class="mt-2">
      通知群组:
      <Select
        class="ml-2 w-[300px]"
        mode="multiple"
        placeholder="请选择群组"
        v-model:value="value.chat_ids"
        :options="chatsOptions"
      />
    </div>
  </div>
</template>
