import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:tickets',
      keepAlive: true,
      order: 1000,
      title: '工单信息',
    },
    name: 'tickets',
    path: '/tickets',
    children: [
      {
        meta: {
          icon: 'lucide:ticket',
          title: '工单列表',
        },
        name: 'tickets_list',
        path: '/tickets/list',
        component: () => import('#/views/ticket/list/index.vue'),
      },
      {
        meta: {
          icon: 'lucide:ticket-plus',
          title: '创建工单',
        },
        name: 'tickets_create',
        path: '/tickets/create',
        component: () => import('#/views/ticket/create/index.vue'),
      },
      {
        meta: {
          title: '工单详情',
          hideInMenu: true,
        },
        name: 'tickets_detail',
        path: '/tickets/detail/:id',
        component: () => import('#/views/ticket/detail/index.vue'),
      },
    ],
  },
];

export default routes;
