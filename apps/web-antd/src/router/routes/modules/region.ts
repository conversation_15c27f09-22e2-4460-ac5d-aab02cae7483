import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'stash:billing-info',
      keepAlive: true,
      order: 980,
      title: '区服信息',
    },
    name: 'regions',
    path: '/regions',
    children: [
      {
        meta: {
          icon: 'eos-icons:cluster-management-outlined',
          title: '区服列表',
        },
        name: 'regions_list',
        path: '/regions/list',
        component: () => import('#/views/region/list/index.vue'),
      },
      {
        meta: {
          icon: 'eos-icons:cluster-role-binding',
          title: '区服模块关系',
        },
        name: 'regions_module',
        path: '/regions/module',
        component: () => import('#/views/region/module/index.vue'),
      },
      {
        meta: {
          icon: 'eos-icons:cluster-management-outlined',
          title: '区服详情',
          hideInMenu: true,
          group: (route: any) => {
            return route.params.name;
          },
        },
        name: 'regions_detail',
        path: '/regions/detail/:name',
        component: () => import('#/views/region/detail/index.vue'),
      },
      {
        meta: {
          icon: 'formkit:group',
          title: '区服分组',
        },
        name: 'region_groups_list',
        path: '/region_groups/list',
        component: () => import('#/views/region_group/list/index.vue'),
      },
      {
        meta: {
          icon: 'carbon:load-balancer-vpc',
          title: '一级DP',
        },
        name: 'global_dp_list',
        path: '/regions/global_dispatch/list',
        component: () => import('#/views/global_dp/list/index.vue'),
      },
      {
        meta: {
          icon: 'carbon:load-balancer-vpc',
          title: '一级DP详情',
          hideInMenu: true,
        },
        name: 'global_dp_detail',
        path: '/regions/global_dispatch/detail/:name',
        component: () => import('#/views/global_dp/detail/index.vue'),
      },
      {
        meta: {
          icon: 'lucide:history',
          title: '版本时间线',
        },
        name: 'region_version_timeline',
        path: '/regions/version_timeline',
        component: () => import('#/views/region/version_timeline/index.vue'),
      },
      {
        meta: {
          title: '流程详情',
          hideInMenu: true,
          group: (route: any) => {
            return route.params.name;
          },
        },
        name: 'region_pipeline_detail',
        path: '/regions/:name/pipeline/:id',
        component: () => import('#/views/pipeline/history/detail/index.vue'),
      },
    ],
  },
];

export default routes;
