import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:rectangle-ellipsis',
      title: '流程模板',
    },
    name: 'custom_pipeline',
    path: '/pipelines/template',
    children: [
      {
        meta: {
          icon: 'lucide:rectangle-ellipsis',
          title: '创建流程模板',
        },
        name: 'pipeline_template_create',
        path: '/pipelines/template/create',
        component: () => import('#/views/pipeline_template/create/index.vue'),
      },
      {
        meta: {
          title: '编辑流程模板',
          hideInMenu: true,
        },
        name: 'pipeline_template_edit',
        path: '/pipelines/template/edit/:code',
        component: () => import('#/views/pipeline_template/edit/index.vue'),
      },
      {
        meta: {
          title: '执行流程模板',
          hideInMenu: true,
        },
        name: 'pipeline_template_exec',
        path: '/pipelines/template/exec/:code',
        component: () => import('#/views/pipeline_template/execute/index.vue'),
      },
      {
        meta: {
          icon: 'lucide:rectangle-ellipsis',
          title: '流程模板列表',
        },
        name: 'pipeline_template_list',
        path: '/pipelines/template/list',
        component: () => import('#/views/pipeline_template/list/index.vue'),
      },
    ],
  },
];

export default routes;
