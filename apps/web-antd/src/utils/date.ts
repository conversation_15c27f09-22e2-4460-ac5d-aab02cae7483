export const GenerateDateStr = (date: Date) => {
  const year = date.getFullYear(); // 获取年份
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份需要+1并补零
  const day = String(date.getDate()).padStart(2, '0'); // 日期补零
  const hours = String(date.getHours()).padStart(2, '0'); // 小时补零
  const minutes = String(date.getMinutes()).padStart(2, '0'); // 分钟补零
  const seconds = String(date.getSeconds()).padStart(2, '0'); // 秒补零

  // 拼接成需要的格式
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
};
