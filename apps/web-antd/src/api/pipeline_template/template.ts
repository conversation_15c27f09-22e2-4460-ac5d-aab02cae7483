import type { PipelineTemplateDetail } from './type';

import { requestClient } from '#/api/request';

export async function createPipelineTemplateApi(data: any) {
  return requestClient.post('/pipeline_templates', data);
}

export async function getPipelineTemplate<PERSON>pi(code: string) {
  return requestClient.get<PipelineTemplateDetail>(
    `/pipeline_templates/${code}`,
  );
}

export async function queryPipelineTemplateApi(
  name: string,
  description: string,
  page: number,
  size: number,
) {
  return requestClient.get(`/pipeline_templates`, {
    params: {
      page,
      size,
      name,
      description,
    },
  });
}

export async function updatePipelineTemplateApi(code: string, data: any) {
  return requestClient.put(`/pipeline_templates/${code}`, data);
}

export async function deletePipelineTemplateApi(code: string) {
  return requestClient.delete(`/pipeline_templates/${code}`);
}

export async function queryPipelineTemplateVersionsApi(
  code: string,
  page: number,
  size: number,
  desc?: string,
) {
  return requestClient.get(`/pipeline_templates/${code}/versions`, {
    params: {
      page,
      size,
      desc,
    },
  });
}
