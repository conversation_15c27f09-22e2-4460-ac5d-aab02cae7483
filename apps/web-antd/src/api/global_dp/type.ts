export interface listGlobalDispatcheQuery {
  page: number;
  size: number;
  name?: string;
  description?: string;
}

export interface GlobalDispatchHostInfoReq {
  page: number;
  size: number;
  hostname?: string;
  is_healthy?: boolean;
  ip?: string;
}

export interface GlobalDispatchProcessInfo {
  is_healthy: boolean;
  healthy_count: number;
  un_healthy_count: number;
}

export interface GlobalDispatchExtraInfo {
  monitor_branch: string;
  global_dispatch_process_info: GlobalDispatchProcessInfo;
}

export interface GlobalDispatchDetail {
  name: string;
  region_list_name: string;
  data_branch: string;
  code_branch: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  global_dispatch_extra_info: GlobalDispatchExtraInfo;
}

export interface queryDpHostData {
  hostname: string;
  ip: string;
  is_healthy: boolean;
  branch: string;
  commit_id: string;
  build_time: string;
  config_hash: string;
}
