import type { PaginationResp } from '../common_type';
import type {
  GlobalDispatchHostInfoReq,
  listGlobalDispatcheQuery,
  queryDpHostData,
} from './type';

import { requestClient } from '#/api/request';

export async function createGlobalDispatch<PERSON>pi(data: any) {
  return requestClient.post('/global/dps', data);
}

export async function getGlobalDispatchApi(name: string) {
  return requestClient.get(`/global/dps/${name}`);
}

export async function queryGlobalDispatchApi(params: listGlobalDispatcheQuery) {
  return requestClient.get(`/global/dps`, {
    params,
  });
}

export async function updateGlobalDispatchApi(name: string, data: any) {
  return requestClient.put(`/global/dps/${name}`, data);
}

export async function deleteGlobalDispatchApi(name: string) {
  return requestClient.delete(`/global/dps/${name}`);
}

export async function getGlobalDispatchHostInfoApi(
  name: string,
  params: GlobalDispatchHostInfoReq,
) {
  return requestClient.get<PaginationResp<queryDpHostData>>(
    `/global/dps/${name}/hosts/info`,
    {
      params,
    },
  );
}

export async function getGlobalDispatchShortcutApi(name: string) {
  return requestClient.get(`/global/dps/${name}/shortcuts`);
}
