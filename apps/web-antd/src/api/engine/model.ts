export const ShortcutPipelineTemplateId = -1;

export interface PipelineLogs {
  stage_idx: number;
  task_idx: number;
  subtask_idx: number;
  log_detail: string;
  status: string;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface Variable {
  name: string;
  key: string;
  type: string;
  value: any;
}

export interface PluginResult {
  is_success: boolean;
  output: string;
  err_msg: string;
}

export interface OutputVariable {
  name: string;
  key: string;
  jsonpath: string;
}

export interface SubtaskPlugin {
  name: string;
  input: Variable[];
  results: PluginResult[];
  output: OutputVariable[];
}
export interface Subtask {
  id: string;
  name: string;
  is_enabled: boolean;
  status: string;
  plugin: SubtaskPlugin;
}

export interface Task {
  id: string;
  name: string;
  status: string;
  is_enabled: boolean;
  subtasks: Subtask[];
}
export interface Stage {
  id: string;
  name: string;
  is_enabled: boolean;
  status: string;
  tasks: Task[];
  trigger: string;
}

export interface Pipeline {
  id: number;
  name: string;
  status: string;
  description: string;
  variables: Variable[];
  stages: Stage[];
  current_stage_idx: number;
  current_task_idx: number;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  ticket_id: number;
  pipeline_template_id: number;
}

export interface PipelineListData {
  pipeline: Pipeline;
  actions: string[];
}

export interface PipelineLogsResponse {
  total: number;
  list: PipelineLogs[];
}
