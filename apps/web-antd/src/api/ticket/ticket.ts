import { requestClient } from '#/api/request';

export async function createTicket<PERSON>pi(data: any) {
  return requestClient.post('/tickets', data);
}

export async function getTicketApi(ticketId: string) {
  return requestClient.get(`/tickets/${ticketId}`);
}

export async function queryTicketApi(
  name: string,
  description: string,
  page: number,
  size: number,
) {
  return requestClient.get(`/tickets`, {
    params: {
      page,
      size,
      name,
      description,
    },
  });
}

export async function actionTicketApi(ticketId: string, action: string) {
  return requestClient.post(`/tickets/${ticketId}/action`, {
    action,
  });
}
