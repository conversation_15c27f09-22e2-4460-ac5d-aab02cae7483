<script setup lang="ts">
import type { TabDefinition } from '@vben-core/typings';

import type { GroupedTabConfig, TabConfig, TabsProps } from '../../types';

import { computed, ref } from 'vue';

import { Pin, X } from '@vben-core/icons';
import { VbenContextMenu, VbenIcon } from '@vben-core/shadcn-ui';

interface Props extends TabsProps {}

defineOptions({
  name: 'VbenTabsChrome',
  // eslint-disable-next-line perfectionist/sort-objects
  inheritAttrs: false,
});

const props = withDefaults(defineProps<Props>(), {
  contentClass: 'vben-tabs-content',
  contextMenus: () => [],
  gap: 7,
  groups: () => [],
  tabs: () => [],
});

const emit = defineEmits<{
  close: [string];
  createGroup: [string];
  deleteGroup: [string];
  moveTabToGroup: [string, string];
  renameGroup: [string, string];
  toggleGroupCollapse: [string];
  unpin: [TabDefinition];
}>();
const active = defineModel<string>('active');

const contentRef = ref();
const tabRef = ref();

const style = computed(() => {
  const { gap } = props;
  return {
    '--gap': `${gap}px`,
  };
});

const tabsView = computed(() => {
  return props.tabs.map((tab) => {
    const { fullPath, meta, name, path } = tab || {};
    const { affixTab, icon, newTabTitle, tabClosable, title } = meta || {};
    let group = (meta as any)?.group;
    if (typeof group === 'function') {
      group = group(tab);
    }
    return {
      affixTab: !!affixTab,
      closable: Reflect.has(meta, 'tabClosable') ? !!tabClosable : true,
      fullPath,
      group, // 从meta中获取分组ID
      icon: icon as string,
      key: fullPath || path,
      meta,
      name,
      path,
      title: (newTabTitle || title || name) as string,
    } as TabConfig;
  });
});

// 分组相关的计算属性
const groupedTabs = computed(() => {
  const groupMap = new Map<null | string, TabConfig[]>();

  // 初始化所有分组
  props.groups?.forEach((group) => {
    groupMap.set(group.name, []);
  });

  // 初始化未分组
  groupMap.set(null, []);

  // 将tabs分配到对应的分组
  tabsView.value.forEach((tab) => {
    const group = tab.group || null;
    const tabs = groupMap.get(group) || [];
    tabs.push(tab);
    groupMap.set(group, tabs);
  });

  const result: GroupedTabConfig[] = [];

  // 添加未分组的tabs
  const ungroupedTabs = groupMap.get(null) || [];
  if (ungroupedTabs.length > 0) {
    result.push({ group: null, tabs: ungroupedTabs });
  }

  // 添加分组的tabs
  props.groups?.forEach((group) => {
    const tabs = groupMap.get(group.name) || [];
    if (tabs.length > 0) {
      result.push({ group, tabs });
    }
  });

  return result;
});

// 分组管理函数
// function toggleGroupCollapse(group: string) {
//   emit('toggleGroupCollapse', group);
// }

function onMouseDown(e: MouseEvent, tab: TabConfig) {
  if (
    e.button === 1 &&
    tab.closable &&
    !tab.affixTab &&
    tabsView.value.length > 1 &&
    props.middleClickToClose
  ) {
    e.preventDefault();
    e.stopPropagation();
    emit('close', tab.key);
  }
}
</script>

<template>
  <div
    ref="contentRef"
    :class="contentClass"
    :style="style"
    class="tabs-chrome !flex h-full w-max overflow-y-hidden pr-6"
  >
    <template v-for="groupedTab in groupedTabs" :key="groupedTab.group?.name">
      <div
        v-if="groupedTab.group"
        class="tabs-chrome__group-header text-muted-foreground ml-4 flex items-center gap-1 px-2 py-1 text-xs font-medium"
      >
        <span
          class="inline-flex cursor-pointer items-center rounded-md bg-gray-600 px-3 py-1 text-xs font-medium text-white transition-colors hover:bg-gray-700"
        >
          {{ groupedTab.group.name }}
        </span>
      </div>
      <TransitionGroup name="slide-left">
        <div
          v-for="(tab, i) in groupedTab.tabs"
          :key="tab.key"
          ref="tabRef"
          :class="[
            {
              'is-active': tab.key === active,
              draggable: !tab.affixTab,
              'affix-tab': tab.affixTab,
              [`group-${groupedTab.group?.name}`]: groupedTab.group !== null,
            },
          ]"
          :data-active-tab="active"
          :data-index="i"
          class="tabs-chrome__item draggable translate-all group relative -mr-3 flex h-full select-none items-center"
          data-tab-item="true"
          @click="active = tab.key"
          @mousedown="onMouseDown($event, tab)"
        >
          <VbenContextMenu
            :handler-data="tab"
            :menus="contextMenus"
            :modal="false"
            item-class="pr-6"
          >
            <div class="relative size-full px-1">
              <!-- divider -->
              <div
                v-if="i !== 0 && tab.key !== active"
                class="tabs-chrome__divider bg-border absolute left-[var(--gap)] top-1/2 z-0 h-4 w-[1px] translate-y-[-50%] transition-all"
              ></div>
              <!-- background -->
              <div
                class="tabs-chrome__background absolute z-[-1] size-full px-[calc(var(--gap)-1px)] py-0 transition-opacity duration-150"
              >
                <div
                  class="tabs-chrome__background-content group-[.is-active]:bg-primary/15 dark:group-[.is-active]:bg-accent h-full rounded-tl-[var(--gap)] rounded-tr-[var(--gap)] duration-150"
                ></div>
                <svg
                  class="tabs-chrome__background-before group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 left-[-1px] fill-transparent transition-all duration-150"
                  height="7"
                  width="7"
                >
                  <path d="M 0 7 A 7 7 0 0 0 7 0 L 7 7 Z" />
                </svg>
                <svg
                  class="tabs-chrome__background-after group-[.is-active]:fill-primary/15 dark:group-[.is-active]:fill-accent absolute bottom-0 right-[-1px] fill-transparent transition-all duration-150"
                  height="7"
                  width="7"
                >
                  <path d="M 0 0 A 7 7 0 0 0 7 7 L 0 7 Z" />
                </svg>
              </div>

              <!-- extra -->
              <div
                class="tabs-chrome__extra absolute right-[var(--gap)] top-1/2 z-[3] size-4 translate-y-[-50%]"
              >
                <!-- close-icon -->
                <X
                  v-show="!tab.affixTab && tabsView.length > 1 && tab.closable"
                  class="hover:bg-accent stroke-accent-foreground/80 hover:stroke-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[2px] size-3 cursor-pointer rounded-full transition-all"
                  @click.stop="() => emit('close', tab.key)"
                />
                <Pin
                  v-show="tab.affixTab && tabsView.length > 1 && tab.closable"
                  class="hover:text-accent-foreground text-accent-foreground/80 group-[.is-active]:text-accent-foreground mt-[1px] size-3.5 cursor-pointer rounded-full transition-all"
                  @click.stop="() => emit('unpin', tab)"
                />
              </div>

              <!-- tab-item-main -->
              <div
                class="tabs-chrome__item-main group-[.is-active]:text-primary dark:group-[.is-active]:text-accent-foreground text-accent-foreground z-[2] mx-[calc(var(--gap)*2)] my-0 flex h-full items-center overflow-hidden rounded-tl-[5px] rounded-tr-[5px] pl-2 pr-4 duration-150"
              >
                <VbenIcon
                  v-if="showIcon"
                  :icon="tab.icon"
                  class="mr-1 flex size-4 items-center overflow-hidden"
                />

                <span class="flex-1 overflow-hidden whitespace-nowrap text-sm">
                  {{ tab.title }}
                </span>
              </div>
            </div>
          </VbenContextMenu>
        </div>
      </TransitionGroup>
    </template>
  </div>
</template>

<style scoped>
.tabs-chrome {
  &__item:not(.dragging) {
    @apply cursor-pointer;

    &:hover:not(.is-active) {
      & + .tabs-chrome__item {
        .tabs-chrome__divider {
          @apply opacity-0;
        }
      }

      .tabs-chrome__divider {
        @apply opacity-0;
      }

      .tabs-chrome__background {
        @apply pb-[2px];

        &-content {
          @apply bg-accent mx-[2px] rounded-md;
        }
      }
    }

    &.is-active {
      @apply z-[2];

      & + .tabs-chrome__item {
        .tabs-chrome__divider {
          @apply opacity-0 !important;
        }
      }
    }
  }
}
</style>
